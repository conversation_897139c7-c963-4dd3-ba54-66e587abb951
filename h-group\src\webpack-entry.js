import React from 'react';
import ReactDOM from 'react-dom';
import App from './App';
import ErrorBoundary from './components/ErrorBoundary';

console.log('تم تحميل webpack-entry.js');

// دالة لتحميل التطبيق
function loadApp() {
  console.log('بدء تحميل التطبيق من webpack...');

  const appDiv = document.getElementById('app');

  if (appDiv) {
    console.log('تم العثور على عنصر #app، بدء تحميل React...');

    try {
      // التحقق من وجود React مُحمل مسبقاً وإلغاء تحميله
      if (window.reactRoot) {
        console.log('إلغاء تحميل React السابق...');
        window.reactRoot.unmount();
        window.reactRoot = null;
      }

      // تنظيف محتوى العنصر بأمان
      while (appDiv.firstChild) {
        appDiv.removeChild(appDiv.firstChild);
      }

      // إنشاء root جديد باستخدام React 18 API
      if (ReactDOM.createRoot) {
        // React 18+
        window.reactRoot = ReactDOM.createRoot(appDiv);
        window.reactRoot.render(
          <ErrorBoundary>
            <App />
          </ErrorBoundary>
        );
      } else {
        // React 17 وما قبل
        ReactDOM.render(
          <ErrorBoundary>
            <App />
          </ErrorBoundary>,
          appDiv
        );
      }

      console.log('تم تحميل تطبيق React بنجاح من webpack');
    } catch (error) {
      console.error('خطأ في تحميل تطبيق React:', error);

      // تنظيف آمن في حالة الخطأ
      try {
        while (appDiv.firstChild) {
          appDiv.removeChild(appDiv.firstChild);
        }
      } catch (cleanupError) {
        console.error('خطأ في تنظيف DOM:', cleanupError);
      }

      appDiv.innerHTML = `
        <div style="padding: 2rem; background: #fee2e2; color: #991b1b; border-radius: 8px; margin: 2rem;">
          <h2>خطأ في تحميل التطبيق</h2>
          <p>حدث خطأ أثناء تحميل التطبيق: ${error.message}</p>
          <pre style="background: #fef2f2; padding: 1rem; border-radius: 4px; margin-top: 1rem; overflow: auto;">${error.stack}</pre>
        </div>
      `;
    }
  } else {
    console.error('لم يتم العثور على عنصر #app');
  }
}

// تحميل التطبيق فوراً إذا كان DOM جاهز، أو انتظار حتى يصبح جاهز
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', loadApp);
} else {
  // DOM جاهز بالفعل
  setTimeout(loadApp, 100); // تأخير قصير للتأكد من تحميل كل شيء
}

// استدعاء إضافي للتأكد
setTimeout(() => {
  if (document.getElementById('app').innerHTML.includes('loading-container')) {
    console.log('التطبيق لا يزال في حالة التحميل، محاولة إعادة التحميل...');
    loadApp();
  }
}, 1000);

// تصدير للاستخدام العام
window.React = React;
window.ReactDOM = ReactDOM;
window.loadApp = loadApp;
