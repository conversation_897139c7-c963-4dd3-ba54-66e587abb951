// هذا الملف هو نقطة الدخول لتطبيق React
console.log('تم تحميل ملف renderer.js');

// التحقق من تحميل bundle.js
document.addEventListener('DOMContentLoaded', () => {
  console.log('بدء تحميل التطبيق...');

  // التحقق من وجود bundle.js محمل بالفعل
  if (window.loadApp && typeof window.loadApp === 'function') {
    console.log('bundle.js محمل بالفعل، تشغيل التطبيق...');
    // لا نحتاج لفعل شيء، bundle.js سيتولى الأمر
  } else {
    console.log('انتظار تحميل bundle.js...');
    // انتظار قصير للتأكد من تحميل bundle.js
    setTimeout(() => {
      if (window.loadApp && typeof window.loadApp === 'function') {
        console.log('تم تحميل bundle.js، تشغيل التطبيق...');
      } else {
        console.error('فشل في تحميل bundle.js');
        const appDiv = document.getElementById('app');
        if (appDiv) {
          appDiv.innerHTML = `
            <div style="padding: 2rem; background: #fee2e2; color: #991b1b; border-radius: 8px; margin: 2rem;">
              <h2>خطأ في تحميل التطبيق</h2>
              <p>فشل في تحميل ملف bundle.js</p>
              <p>تأكد من وجود الملف في مجلد dist</p>
            </div>
          `;
        }
      }
    }, 1000);
  }
});
