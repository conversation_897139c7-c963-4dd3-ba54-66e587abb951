const React = require('react');
const { useState, useEffect } = React;
const { Link, useParams, useNavigate } = require('react-router-dom');
const { formatCurrency, formatDate } = require('../../utils/formatters');

// مكون تشخيص مؤقت
const DebugInfo = ({ activeTab, section, loading, tabLoading, dataLoaded, location }) => {
  if (process.env.NODE_ENV !== 'development') return null;

  return React.createElement('div', {
    style: {
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: '#fff',
      border: '2px solid #ff6b35',
      padding: '10px',
      fontSize: '12px',
      zIndex: 9999,
      borderRadius: '5px',
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      maxWidth: '300px'
    }
  },
    React.createElement('div', null, `Active Tab: ${activeTab}`),
    React.createElement('div', null, `URL Section: ${section || 'none'}`),
    React.createElement('div', null, `Current Path: ${location?.pathname || 'unknown'}`),
    React.createElement('div', null, `Loading: ${loading}`),
    React.createElement('div', null, `Tab Loading: ${tabLoading}`),
    React.createElement('div', null, `Data Loaded: ${dataLoaded}`)
  );
};

const ProductionManagement = () => {
  const { section } = useParams();
  const navigate = useNavigate();
  const location = window.location;
  const [activeTab, setActiveTab] = useState(section || 'materials');
  const [dashboardData, setDashboardData] = useState({
    materials: [],
    inventory: [],
    reservations: [],
    veneer: [],
    lowStockItems: [],
    totalMaterials: 0,
    totalValue: 0,
    activeReservations: 0
  });
  const [loading, setLoading] = useState(true);
  const [tabLoading, setTabLoading] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false);

  useEffect(() => {
    loadProductionData();
  }, []);

  // تحديث التبويب النشط عند تغيير المسار
  useEffect(() => {
    console.log('تغيير المسار - section:', section);
    if (section && ['materials', 'veneer', 'inventory', 'reservations'].includes(section)) {
      console.log('تحديث التبويب النشط إلى:', section);
      setActiveTab(section);
    } else if (!section) {
      // إذا لم يكن هناك قسم محدد، اعرض التبويب الافتراضي
      console.log('لا يوجد قسم محدد، استخدام التبويب الافتراضي: materials');
      setActiveTab('materials');
      // تحديث المسار ليعكس التبويب الافتراضي
      navigate('/production/materials', { replace: true });
    }
  }, [section, navigate]);

  const loadProductionData = async () => {
    try {
      setLoading(true);

      const [materials, inventory, reservations] = await Promise.all([
        window.api.materials.getAll(),
        window.api.inventory.getAll(),
        window.api.materialReservations.getAll()
      ]);

      // التأكد من أن البيانات ليست null أو undefined
      const safeMaterials = materials || [];
      const safeInventory = inventory || [];
      const safeReservations = reservations || [];

      // فلترة الفونير
      const veneer = safeMaterials.filter(m =>
        m.name?.toLowerCase().includes('فونير') ||
        m.name?.toLowerCase().includes('قشرة') ||
        m.category === 'veneer'
      );

      // العناصر منخفضة المخزون
      const lowStockItems = safeInventory.filter(item =>
        item.quantity <= (item.min_quantity || 0)
      );

      // حساب القيمة الإجمالية
      const totalValue = safeInventory.reduce((sum, item) => {
        const material = safeMaterials.find(m => m.id === item.material_id);
        return sum + (item.quantity * (material?.cost_per_unit || 0));
      }, 0);

      // الحجوزات النشطة
      const activeReservations = safeReservations.filter(r => r.status === 'نشط').length;

      setDashboardData({
        materials: safeMaterials,
        inventory: safeInventory,
        reservations: safeReservations,
        veneer,
        lowStockItems,
        totalMaterials: safeMaterials.length,
        totalValue,
        activeReservations
      });

      setDataLoaded(true);
      console.log('تم تحميل البيانات بنجاح:', {
        materials: safeMaterials.length,
        inventory: safeInventory.length,
        reservations: safeReservations.length,
        veneer: veneer.length,
        lowStockItems: lowStockItems.length
      });

    } catch (error) {
      console.error('خطأ في تحميل بيانات الإنتاج:', error);
      setDataLoaded(false);
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'materials', label: 'المواد الخام', icon: 'fas fa-boxes' },
    { id: 'veneer', label: 'الفونير والقشرة', icon: 'fas fa-layer-group' },
    { id: 'inventory', label: 'المخزون والجرد', icon: 'fas fa-warehouse' },
    { id: 'reservations', label: 'حجوزات المواد', icon: 'fas fa-bookmark' }
  ];

  // دالة تغيير التبويب مع تحديث المسار
  const handleTabChange = async (tabId) => {
    if (tabId === activeTab) {
      console.log('التبويب نفسه نشط بالفعل:', tabId);
      return; // تجنب إعادة التحميل للتبويب النشط
    }

    console.log('تغيير التبويب من', activeTab, 'إلى', tabId);
    setTabLoading(true);

    // تحديث المسار أولاً
    navigate(`/production/${tabId}`);

    // تحديث التبويب النشط
    setActiveTab(tabId);

    // محاكاة تحميل البيانات (يمكن إزالة هذا إذا لم تكن هناك حاجة لتحميل بيانات إضافية)
    setTimeout(() => {
      setTabLoading(false);
      console.log('انتهى تحميل التبويب:', tabId);
    }, 300);
  };

  if (loading) {
    return React.createElement('div', { className: 'loading-container' },
      React.createElement('div', { className: 'loading-spinner' }),
      React.createElement('p', null, 'جاري تحميل بيانات الإنتاج...')
    );
  }

  return React.createElement('div', { className: 'production-management professional-page' },
    // مكون التشخيص
    React.createElement(DebugInfo, {
      activeTab,
      section,
      loading,
      tabLoading,
      dataLoaded,
      location
    }),
    // رأس الصفحة
    React.createElement('div', { className: 'page-header modern-header' },
      React.createElement('div', { className: 'header-content' },
        React.createElement('h1', { className: 'page-title' },
          React.createElement('i', { className: 'fas fa-industry' }),
          ' إدارة الإنتاج والمواد'
        ),
        React.createElement('p', { className: 'page-subtitle' },
          'إدارة شاملة للمواد الخام والمخزون وحجوزات الإنتاج'
        )
      )
    ),

    // إحصائيات سريعة
    React.createElement('div', { className: 'stats-grid' },
      React.createElement('div', { className: 'stat-card primary' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-boxes' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'إجمالي المواد'),
          React.createElement('div', { className: 'stat-value' }, dashboardData.totalMaterials),
          React.createElement('div', { className: 'stat-detail' }, 'نوع مادة خام')
        )
      ),
      React.createElement('div', { className: 'stat-card success' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-dollar-sign' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'قيمة المخزون'),
          React.createElement('div', { className: 'stat-value' }, formatCurrency(dashboardData.totalValue)),
          React.createElement('div', { className: 'stat-detail' }, 'القيمة الإجمالية')
        )
      ),
      React.createElement('div', { className: 'stat-card warning' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-exclamation-triangle' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'تحتاج تجديد'),
          React.createElement('div', { className: 'stat-value' }, dashboardData.lowStockItems.length),
          React.createElement('div', { className: 'stat-detail' }, 'مادة منخفضة المخزون')
        )
      ),
      React.createElement('div', { className: 'stat-card info' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-bookmark' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'الحجوزات النشطة'),
          React.createElement('div', { className: 'stat-value' }, dashboardData.activeReservations),
          React.createElement('div', { className: 'stat-detail' }, 'حجز نشط')
        )
      )
    ),

    // التبويبات
    React.createElement('div', { className: 'modern-card glass-card' },
      React.createElement('div', { className: 'card-header modern-card-header' },
        React.createElement('div', { className: 'tabs-container' },
          tabs.map(tab =>
            React.createElement('button', {
              key: tab.id,
              className: `tab-btn ${activeTab === tab.id ? 'active' : ''}`,
              onClick: () => {
                console.log('تم النقر على التبويب:', tab.id);
                handleTabChange(tab.id);
              },
              style: {
                backgroundColor: activeTab === tab.id ? '#1e40af' : '#f8f9fa',
                color: activeTab === tab.id ? 'white' : '#333'
              }
            },
              React.createElement('i', { className: tab.icon }),
              React.createElement('span', null, tab.label)
            )
          )
        )
      ),

      React.createElement('div', {
        className: `card-body ${tabLoading ? 'loading' : ''}`
      },
        // مؤشر التبويب النشط
        React.createElement('div', {
          style: {
            padding: '10px',
            background: '#f8f9fa',
            marginBottom: '15px',
            borderRadius: '5px',
            border: '1px solid #dee2e6'
          }
        },
          React.createElement('strong', null, `التبويب النشط: ${activeTab}`),
          React.createElement('span', { style: { marginLeft: '15px' } }, `البيانات محملة: ${dataLoaded ? 'نعم' : 'لا'}`),
          React.createElement('span', { style: { marginLeft: '15px' } }, `عدد المواد: ${dashboardData.materials.length}`)
        ),

        // محتوى افتراضي إذا لم يكن هناك تبويب نشط
        !activeTab && React.createElement('div', { className: 'alert alert-warning' },
          React.createElement('h4', null, 'لا يوجد تبويب نشط'),
          React.createElement('p', null, 'يرجى اختيار تبويب من الأعلى لعرض المحتوى')
        ),

        // محتوى التبويب النشط
        activeTab === 'materials' && React.createElement('div', {
          key: 'materials-tab',
          className: 'tab-content'
        },
          React.createElement('div', {
            style: {
              padding: '10px',
              background: '#d4edda',
              marginBottom: '15px',
              borderRadius: '5px'
            }
          }, `تبويب المواد نشط - عدد العناصر: ${dashboardData.materials.length}`),
          React.createElement('div', { className: 'section-header' },
            React.createElement('h3', null, 'المواد الخام'),
            React.createElement('div', { className: 'section-actions' },
              React.createElement(Link, {
                to: '/materials/new',
                className: 'btn btn-primary modern-btn'
              },
                React.createElement('i', { className: 'fas fa-plus' }),
                ' إضافة مادة جديدة'
              )
            )
          ),
          // عرض رسالة إذا لم تكن هناك مواد
          dashboardData.materials.length === 0
            ? React.createElement('div', { className: 'alert alert-info' },
                React.createElement('h4', null, 'لا توجد مواد خام'),
                React.createElement('p', null, 'لم يتم العثور على أي مواد خام في النظام'),
                React.createElement(Link, {
                  to: '/materials/new',
                  className: 'btn btn-primary'
                }, 'إضافة مادة خام جديدة')
              )
            : React.createElement('div', { className: 'table-responsive' },
                React.createElement('table', { className: 'modern-table professional-table' },
              React.createElement('thead', null,
                React.createElement('tr', null,
                  React.createElement('th', null, 'اسم المادة'),
                  React.createElement('th', null, 'الوحدة'),
                  React.createElement('th', null, 'التكلفة/وحدة'),
                  React.createElement('th', null, 'الكمية المتاحة'),
                  React.createElement('th', null, 'الحد الأدنى'),
                  React.createElement('th', null, 'الحالة'),
                  React.createElement('th', null, 'الإجراءات')
                )
              ),
              React.createElement('tbody', null,
                dashboardData.materials.slice(0, 10).map(material => {
                  const inventoryItem = dashboardData.inventory.find(i => i.material_id === material.id);
                  const currentStock = inventoryItem?.quantity || 0;
                  const isLowStock = currentStock <= (material.min_quantity || 0);

                  return React.createElement('tr', { key: material.id },
                    React.createElement('td', null, material.name),
                    React.createElement('td', null, material.unit || '-'),
                    React.createElement('td', null, formatCurrency(material.cost_per_unit || 0)),
                    React.createElement('td', null, currentStock),
                    React.createElement('td', null, material.min_quantity || 0),
                    React.createElement('td', null,
                      React.createElement('span', {
                        className: `status-badge ${isLowStock ? 'danger' : 'success'}`
                      }, isLowStock ? 'منخفض' : 'متوفر')
                    ),
                    React.createElement('td', null,
                      React.createElement('div', { className: 'action-buttons' },
                        React.createElement(Link, {
                          to: `/materials/${material.id}`,
                          className: 'action-btn info-btn',
                          title: 'عرض التفاصيل'
                        },
                          React.createElement('i', { className: 'fas fa-eye' })
                        ),
                        React.createElement(Link, {
                          to: `/materials/edit/${material.id}`,
                          className: 'action-btn primary-btn',
                          title: 'تعديل'
                        },
                          React.createElement('i', { className: 'fas fa-edit' })
                        )
                      )
                    )
                  );
                })
              )
            )
          ),
          dashboardData.materials.length > 10 && React.createElement('div', { className: 'text-center mt-3' },
            React.createElement(Link, {
              to: '/materials',
              className: 'btn btn-outline-primary'
            }, 'عرض جميع المواد')
          )
        ),

        activeTab === 'veneer' && React.createElement('div', {
          key: 'veneer-tab',
          className: 'tab-content'
        },
          React.createElement('div', {
            style: {
              padding: '10px',
              background: '#e8f5e8',
              marginBottom: '15px',
              borderRadius: '5px'
            }
          }, `تبويب الفونير نشط - عدد العناصر: ${dashboardData.veneer.length}`),
          React.createElement('div', { className: 'section-header' },
            React.createElement('h3', null, 'الفونير والقشرة'),
            React.createElement('div', { className: 'section-actions' },
              React.createElement(Link, {
                to: '/materials/new?category=veneer',
                className: 'btn btn-primary modern-btn'
              },
                React.createElement('i', { className: 'fas fa-plus' }),
                ' إضافة فونير جديد'
              )
            )
          ),
          dashboardData.veneer.length === 0
            ? React.createElement('div', { className: 'empty-state' },
                React.createElement('i', { className: 'fas fa-layer-group' }),
                React.createElement('h3', null, 'لا يوجد فونير'),
                React.createElement('p', null, 'لم يتم العثور على مواد فونير أو قشرة')
              )
            : React.createElement('div', { className: 'veneer-grid' },
                dashboardData.veneer.map(veneer => {
                  const inventoryItem = dashboardData.inventory.find(i => i.material_id === veneer.id);
                  const currentStock = inventoryItem?.quantity || 0;

                  return React.createElement('div', { key: veneer.id, className: 'veneer-card' },
                    React.createElement('div', { className: 'veneer-header' },
                      React.createElement('h4', null, veneer.name),
                      React.createElement('span', { className: 'veneer-unit' }, veneer.unit || 'متر مربع')
                    ),
                    React.createElement('div', { className: 'veneer-details' },
                      React.createElement('div', { className: 'detail-item' },
                        React.createElement('span', { className: 'label' }, 'الكمية المتاحة:'),
                        React.createElement('span', { className: 'value' }, currentStock)
                      ),
                      React.createElement('div', { className: 'detail-item' },
                        React.createElement('span', { className: 'label' }, 'التكلفة/وحدة:'),
                        React.createElement('span', { className: 'value' }, formatCurrency(veneer.cost_per_unit || 0))
                      )
                    ),
                    React.createElement('div', { className: 'veneer-actions' },
                      React.createElement(Link, {
                        to: `/materials/${veneer.id}`,
                        className: 'btn btn-sm btn-outline-primary'
                      }, 'عرض التفاصيل')
                    )
                  );
                })
              )
        ),

        activeTab === 'inventory' && React.createElement('div', {
          key: 'inventory-tab',
          className: 'tab-content'
        },
          React.createElement('div', {
            style: {
              padding: '10px',
              background: '#fff3cd',
              marginBottom: '15px',
              borderRadius: '5px'
            }
          }, `تبويب المخزون نشط - عدد العناصر: ${dashboardData.inventory.length}`),
          React.createElement('div', { className: 'section-header' },
            React.createElement('h3', null, 'المخزون والجرد'),
            React.createElement('div', { className: 'section-actions' },
              React.createElement('button', {
                className: 'btn btn-success modern-btn',
                onClick: () => window.location.reload()
              },
                React.createElement('i', { className: 'fas fa-sync' }),
                ' تحديث المخزون'
              )
            )
          ),

          // تنبيهات المخزون المنخفض
          dashboardData.lowStockItems.length > 0 && React.createElement('div', { className: 'alert alert-warning mb-4' },
            React.createElement('h5', null,
              React.createElement('i', { className: 'fas fa-exclamation-triangle' }),
              ' تحذير: مواد تحتاج إعادة تجديد'
            ),
            React.createElement('div', { className: 'low-stock-items' },
              dashboardData.lowStockItems.slice(0, 5).map(item => {
                const material = dashboardData.materials.find(m => m.id === item.material_id);
                return React.createElement('div', { key: item.id, className: 'low-stock-item' },
                  React.createElement('strong', null, material?.name || 'مادة غير معروفة'),
                  React.createElement('span', null, ` - الكمية الحالية: ${item.quantity} (الحد الأدنى: ${material?.min_quantity || 0})`)
                );
              })
            )
          ),

          React.createElement('div', { className: 'table-responsive' },
            React.createElement('table', { className: 'modern-table professional-table' },
              React.createElement('thead', null,
                React.createElement('tr', null,
                  React.createElement('th', null, 'المادة'),
                  React.createElement('th', null, 'الكمية الحالية'),
                  React.createElement('th', null, 'الحد الأدنى'),
                  React.createElement('th', null, 'القيمة'),
                  React.createElement('th', null, 'آخر تحديث'),
                  React.createElement('th', null, 'الحالة')
                )
              ),
              React.createElement('tbody', null,
                dashboardData.inventory.map(item => {
                  const material = dashboardData.materials.find(m => m.id === item.material_id);
                  const isLowStock = item.quantity <= (material?.min_quantity || 0);
                  const value = item.quantity * (material?.cost_per_unit || 0);

                  return React.createElement('tr', { key: item.id },
                    React.createElement('td', null, material?.name || 'مادة غير معروفة'),
                    React.createElement('td', null, `${item.quantity} ${material?.unit || ''}`),
                    React.createElement('td', null, material?.min_quantity || 0),
                    React.createElement('td', null, formatCurrency(value)),
                    React.createElement('td', null, formatDate(item.last_updated)),
                    React.createElement('td', null,
                      React.createElement('span', {
                        className: `status-badge ${isLowStock ? 'danger' : 'success'}`
                      }, isLowStock ? 'منخفض' : 'متوفر')
                    )
                  );
                })
              )
            )
          )
        ),

        activeTab === 'reservations' && React.createElement('div', {
          key: 'reservations-tab',
          className: 'tab-content'
        },
          React.createElement('div', {
            style: {
              padding: '10px',
              background: '#d1ecf1',
              marginBottom: '15px',
              borderRadius: '5px'
            }
          }, `تبويب الحجوزات نشط - عدد العناصر: ${dashboardData.reservations.length}`),
          React.createElement('div', { className: 'section-header' },
            React.createElement('h3', null, 'حجوزات المواد'),
            React.createElement('div', { className: 'section-actions' },
              React.createElement(Link, {
                to: '/materials/reservations/new',
                className: 'btn btn-primary modern-btn'
              },
                React.createElement('i', { className: 'fas fa-plus' }),
                ' حجز جديد'
              )
            )
          ),
          dashboardData.reservations.length === 0
            ? React.createElement('div', { className: 'empty-state' },
                React.createElement('i', { className: 'fas fa-bookmark' }),
                React.createElement('h3', null, 'لا توجد حجوزات'),
                React.createElement('p', null, 'لم يتم العثور على حجوزات مواد')
              )
            : React.createElement('div', { className: 'table-responsive' },
                React.createElement('table', { className: 'modern-table professional-table' },
                  React.createElement('thead', null,
                    React.createElement('tr', null,
                      React.createElement('th', null, 'رقم الطلب'),
                      React.createElement('th', null, 'المادة'),
                      React.createElement('th', null, 'الكمية المحجوزة'),
                      React.createElement('th', null, 'تاريخ الحجز'),
                      React.createElement('th', null, 'الحالة'),
                      React.createElement('th', null, 'الإجراءات')
                    )
                  ),
                  React.createElement('tbody', null,
                    dashboardData.reservations.slice(0, 10).map(reservation => {
                      const material = dashboardData.materials.find(m => m.id === reservation.material_id);

                      return React.createElement('tr', { key: reservation.id },
                        React.createElement('td', null, reservation.order_number || '-'),
                        React.createElement('td', null, material?.name || 'مادة غير معروفة'),
                        React.createElement('td', null, `${reservation.reserved_quantity} ${material?.unit || ''}`),
                        React.createElement('td', null, formatDate(reservation.reserved_at)),
                        React.createElement('td', null,
                          React.createElement('span', {
                            className: `status-badge ${
                              reservation.status === 'نشط' ? 'success' :
                              reservation.status === 'مستخدم' ? 'info' :
                              reservation.status === 'ملغي' ? 'danger' : 'secondary'
                            }`
                          }, reservation.status)
                        ),
                        React.createElement('td', null,
                          React.createElement('div', { className: 'action-buttons' },
                            React.createElement('button', {
                              className: 'action-btn info-btn',
                              title: 'عرض التفاصيل'
                            },
                              React.createElement('i', { className: 'fas fa-eye' })
                            )
                          )
                        )
                      );
                    })
                  )
                )
              )
        ),

        // محتوى تشخيصي إضافي
        React.createElement('div', {
          style: {
            marginTop: '20px',
            padding: '15px',
            background: '#f8f9fa',
            border: '1px solid #dee2e6',
            borderRadius: '5px'
          }
        },
          React.createElement('h5', null, 'معلومات التشخيص:'),
          React.createElement('div', null, `التبويب النشط: ${activeTab}`),
          React.createElement('div', null, `حالة التحميل: ${loading ? 'جاري التحميل' : 'مكتمل'}`),
          React.createElement('div', null, `حالة تحميل التبويب: ${tabLoading ? 'جاري التحميل' : 'مكتمل'}`),
          React.createElement('div', null, `البيانات محملة: ${dataLoaded ? 'نعم' : 'لا'}`),
          React.createElement('div', null, `عدد المواد: ${dashboardData.materials.length}`),
          React.createElement('div', null, `عدد الفونير: ${dashboardData.veneer.length}`),
          React.createElement('div', null, `عدد المخزون: ${dashboardData.inventory.length}`),
          React.createElement('div', null, `عدد الحجوزات: ${dashboardData.reservations.length}`)
        )
      )
    )
  );
};

module.exports = ProductionManagement;
