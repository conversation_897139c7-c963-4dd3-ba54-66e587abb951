const React = require('react');
const { useState } = React;
const { Link, NavLink, useNavigate, useLocation } = require('react-router-dom');
const { useAuth } = require('../../contexts/AuthContext');
const { useSettings } = require('../../contexts/SettingsContext');
const { useNotifications } = require('../../contexts/NotificationsContext');
const NotificationsMenu = require('../notifications/NotificationsMenu');

const Navbar = () => {
  const { currentUser, logout } = useAuth();
  const { settings } = useSettings();
  const { unreadCount } = useNotifications();
  const navigate = useNavigate();
  const location = useLocation();
  const [showUserMenu, setShowUserMenu] = useState(false);

  // التعامل مع تسجيل الخروج
  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
    }
  };

  // التبديل بين إظهار وإخفاء قائمة المستخدم
  const toggleUserMenu = () => {
    setShowUserMenu(!showUserMenu);
  };

  // دالة للتحقق من النشاط للأقسام الفرعية
  const isActiveSection = (basePath) => {
    return location.pathname.startsWith(basePath);
  };

  // التحقق من وجود المستخدم
  if (!currentUser) return null;

  return React.createElement('header', null,
    // الشريط العلوي الرئيسي
    React.createElement('nav', { className: 'navbar' },
      React.createElement('div', { className: 'navbar-container' },
        React.createElement(Link, {
          to: '/',
          className: 'navbar-brand'
        },
          React.createElement('i', { className: 'fas fa-industry' }),
          'H Group'
        ),
        React.createElement('div', { className: 'navbar-menu' },
          React.createElement(NotificationsMenu),
          React.createElement('div', { className: 'user-menu' },
            React.createElement('button', {
              className: 'navbar-item user-menu-button',
              onClick: toggleUserMenu
            },
              React.createElement('span', null, currentUser.full_name || currentUser.username),
              React.createElement('i', { className: 'fas fa-user' })
            ),
            showUserMenu && React.createElement('div', { className: 'user-menu-dropdown' },
              React.createElement(Link, { to: '/profile', className: 'user-menu-item' },
                React.createElement('i', { className: 'fas fa-user-circle' }),
                React.createElement('span', null, 'الملف الشخصي')
              ),
              currentUser.role === 'admin' && React.createElement(Link, { to: '/settings', className: 'user-menu-item' },
                React.createElement('i', { className: 'fas fa-cog' }),
                React.createElement('span', null, 'الإعدادات')
              ),
              React.createElement('button', {
                className: 'user-menu-item logout-button',
                onClick: handleLogout
              },
                React.createElement('i', { className: 'fas fa-sign-out-alt' }),
                React.createElement('span', null, 'تسجيل الخروج')
              )
            )
          )
        )
      )
    ),

    // شريط التنقل الرئيسي
    React.createElement('nav', { className: 'main-navigation' },
      React.createElement('div', { className: 'nav-container' },
        React.createElement('div', { className: 'nav-menu' },
          // لوحة التحكم
          React.createElement(NavLink, {
            to: '/',
            className: ({ isActive }) => isActive ? 'nav-item active' : 'nav-item',
            end: true
          },
            React.createElement('i', { className: 'fas fa-tachometer-alt' }),
            React.createElement('span', null, 'لوحة التحكم')
          ),

          // الطلبات المخصصة
          React.createElement(NavLink, {
            to: '/orders',
            className: ({ isActive }) => isActive ? 'nav-item active' : 'nav-item'
          },
            React.createElement('i', { className: 'fas fa-clipboard-list' }),
            React.createElement('span', null, 'الطلبات المخصصة')
          ),

          // العملاء
          React.createElement(NavLink, {
            to: '/customers',
            className: ({ isActive }) => isActive ? 'nav-item active' : 'nav-item'
          },
            React.createElement('i', { className: 'fas fa-users' }),
            React.createElement('span', null, 'العملاء')
          ),

          // إدارة الإنتاج
          React.createElement(NavLink, {
            to: '/production',
            className: () => isActiveSection('/production') ? 'nav-item active' : 'nav-item'
          },
            React.createElement('i', { className: 'fas fa-industry' }),
            React.createElement('span', null, 'إدارة الإنتاج')
          ),

          // الموارد البشرية
          React.createElement(NavLink, {
            to: '/hr',
            className: () => isActiveSection('/hr') ? 'nav-item active' : 'nav-item'
          },
            React.createElement('i', { className: 'fas fa-users-cog' }),
            React.createElement('span', null, 'الموارد البشرية')
          ),

          // الإدارة المالية
          React.createElement(NavLink, {
            to: '/financial',
            className: () => isActiveSection('/financial') ? 'nav-item active' : 'nav-item'
          },
            React.createElement('i', { className: 'fas fa-chart-line' }),
            React.createElement('span', null, 'الإدارة المالية')
          ),

          // التقارير
          React.createElement(NavLink, {
            to: '/reports',
            className: ({ isActive }) => isActive ? 'nav-item active' : 'nav-item'
          },
            React.createElement('i', { className: 'fas fa-chart-bar' }),
            React.createElement('span', null, 'التقارير')
          ),

          // الإعدادات (للمدير فقط)
          currentUser.role === 'admin' && React.createElement(NavLink, {
            to: '/settings',
            className: ({ isActive }) => isActive ? 'nav-item active' : 'nav-item'
          },
            React.createElement('i', { className: 'fas fa-cog' }),
            React.createElement('span', null, 'الإعدادات')
          )
        )
      )
    )
  );
};

module.exports = Navbar;
