const React = require('react');
const { useLocation, useParams } = require('react-router-dom');

/**
 * مكون تشخيص التبويبات - لمساعدة في تشخيص مشاكل التبويبات
 * يُستخدم فقط في بيئة التطوير
 */
const TabsDiagnostic = ({ 
  activeTab, 
  availableTabs, 
  basePath, 
  showDiagnostic = false 
}) => {
  const location = useLocation();
  const params = useParams();

  // عرض التشخيص فقط في بيئة التطوير أو عند الطلب
  if (!showDiagnostic && process.env.NODE_ENV !== 'development') {
    return null;
  }

  const diagnosticInfo = {
    currentPath: location.pathname,
    currentSearch: location.search,
    currentHash: location.hash,
    urlParams: params,
    activeTab,
    availableTabs: availableTabs || [],
    basePath,
    expectedSection: params.section,
    tabsMatch: availableTabs ? availableTabs.includes(activeTab) : false,
    urlSectionMatch: params.section === activeTab
  };

  return React.createElement('div', {
    className: 'tabs-diagnostic',
    style: {
      position: 'fixed',
      bottom: '20px',
      left: '20px',
      background: '#fff',
      border: '2px solid #ff6b35',
      borderRadius: '8px',
      padding: '1rem',
      fontSize: '0.75rem',
      maxWidth: '300px',
      zIndex: 9999,
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
      fontFamily: 'monospace'
    }
  },
    React.createElement('h4', {
      style: { 
        margin: '0 0 0.5rem 0', 
        color: '#ff6b35',
        fontSize: '0.85rem'
      }
    }, '🔍 تشخيص التبويبات'),
    
    React.createElement('div', { style: { marginBottom: '0.5rem' } },
      React.createElement('strong', null, 'المسار الحالي: '),
      React.createElement('code', null, diagnosticInfo.currentPath)
    ),
    
    React.createElement('div', { style: { marginBottom: '0.5rem' } },
      React.createElement('strong', null, 'التبويب النشط: '),
      React.createElement('code', { 
        style: { 
          color: diagnosticInfo.tabsMatch ? '#28a745' : '#dc3545' 
        } 
      }, diagnosticInfo.activeTab || 'غير محدد')
    ),
    
    React.createElement('div', { style: { marginBottom: '0.5rem' } },
      React.createElement('strong', null, 'قسم URL: '),
      React.createElement('code', null, diagnosticInfo.expectedSection || 'غير محدد')
    ),
    
    React.createElement('div', { style: { marginBottom: '0.5rem' } },
      React.createElement('strong', null, 'التبويبات المتاحة: '),
      React.createElement('code', null, 
        diagnosticInfo.availableTabs.length > 0 
          ? diagnosticInfo.availableTabs.join(', ')
          : 'لا توجد'
      )
    ),
    
    React.createElement('div', { style: { marginBottom: '0.5rem' } },
      React.createElement('strong', null, 'حالة التطابق: '),
      React.createElement('span', {
        style: { 
          color: diagnosticInfo.urlSectionMatch ? '#28a745' : '#dc3545',
          fontWeight: 'bold'
        }
      }, diagnosticInfo.urlSectionMatch ? '✅ متطابق' : '❌ غير متطابق')
    ),
    
    // معلومات إضافية للتشخيص
    React.createElement('details', { style: { marginTop: '0.5rem' } },
      React.createElement('summary', { 
        style: { 
          cursor: 'pointer', 
          color: '#2c5aa0',
          fontSize: '0.7rem'
        } 
      }, 'تفاصيل إضافية'),
      
      React.createElement('div', { style: { marginTop: '0.25rem', fontSize: '0.65rem' } },
        React.createElement('div', null, `المسار الأساسي: ${diagnosticInfo.basePath || 'غير محدد'}`),
        React.createElement('div', null, `معاملات URL: ${JSON.stringify(diagnosticInfo.urlParams)}`),
        React.createElement('div', null, `البحث: ${diagnosticInfo.currentSearch || 'فارغ'}`),
        React.createElement('div', null, `الهاش: ${diagnosticInfo.currentHash || 'فارغ'}`)
      )
    )
  );
};

/**
 * مكون مساعد لعرض حالة التبويبات في وحدة التحكم
 */
const logTabsState = (componentName, state) => {
  if (process.env.NODE_ENV === 'development') {
    console.group(`🔍 ${componentName} - حالة التبويبات`);
    console.log('الحالة الحالية:', state);
    console.log('الوقت:', new Date().toLocaleTimeString('ar-SA'));
    console.groupEnd();
  }
};

/**
 * Hook مخصص لتشخيص التبويبات
 */
const useTabsDiagnostic = (componentName, tabsState) => {
  React.useEffect(() => {
    logTabsState(componentName, tabsState);
  }, [componentName, tabsState]);

  return {
    logState: () => logTabsState(componentName, tabsState),
    diagnosticComponent: (showDiagnostic = false) => 
      React.createElement(TabsDiagnostic, {
        ...tabsState,
        showDiagnostic
      })
  };
};

module.exports = {
  TabsDiagnostic,
  logTabsState,
  useTabsDiagnostic
};
