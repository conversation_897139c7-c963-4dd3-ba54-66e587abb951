const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// تعريف واجهة برمجة التطبيق (API) للتواصل بين العمليات الرئيسية والعمليات المعروضة
window.electronAPI = {
  // وظائف العملاء
  customers: {
    getAll: () => ipcRenderer.invoke('customers:getAll'),
    getById: (id) => ipcRenderer.invoke('customers:getById', id),
    create: (customer) => ipcRenderer.invoke('customers:create', customer),
    update: (id, customer) => ipcRenderer.invoke('customers:update', id, customer),
    delete: (id) => ipcRenderer.invoke('customers:delete', id),
    search: (query) => ipcRenderer.invoke('customers:search', query)
  },



  // وظائف مراحل الإنتاج
  productionStages: {
    getByOrderId: (orderId) => ipcRenderer.invoke('productionStages:getByOrderId', orderId),
    create: (stage) => ipcRenderer.invoke('productionStages:create', stage),
    update: (id, stage) => ipcRenderer.invoke('productionStages:update', id, stage),
    delete: (id) => ipcRenderer.invoke('productionStages:delete', id),
    updateStatus: (id, status, completionPercentage) => ipcRenderer.invoke('productionStages:updateStatus', id, status, completionPercentage)
  },

  // وظائف المواصفات المخصصة
  customSpecifications: {
    getByOrderId: (orderId) => ipcRenderer.invoke('customSpecifications:getByOrderId', orderId),
    create: (specification) => ipcRenderer.invoke('customSpecifications:create', specification),
    update: (id, specification) => ipcRenderer.invoke('customSpecifications:update', id, specification),
    delete: (id) => ipcRenderer.invoke('customSpecifications:delete', id)
  },

  // وظائف حجز المواد
  materialReservations: {
    getAll: () => ipcRenderer.invoke('materialReservations:getAll'),
    create: (reservation) => ipcRenderer.invoke('materialReservations:create', reservation),
    getByOrderId: (orderId) => ipcRenderer.invoke('materialReservations:getByOrderId', orderId),
    getByMaterialId: (materialId) => ipcRenderer.invoke('materialReservations:getByMaterialId', materialId),
    use: (reservationId, usedQuantity) => ipcRenderer.invoke('materialReservations:use', reservationId, usedQuantity),
    cancel: (reservationId) => ipcRenderer.invoke('materialReservations:cancel', reservationId)
  },

  // وظائف فحص الجودة
  qualityCheckpoints: {
    getByStageId: (stageId) => ipcRenderer.invoke('qualityCheckpoints:getByStageId', stageId),
    create: (checkpoint) => ipcRenderer.invoke('qualityCheckpoints:create', checkpoint)
  },

  qualityChecks: {
    getByOrderId: (orderId) => ipcRenderer.invoke('qualityChecks:getByOrderId', orderId),
    create: (qualityCheck) => ipcRenderer.invoke('qualityChecks:create', qualityCheck),
    update: (id, qualityCheck) => ipcRenderer.invoke('qualityChecks:update', id, qualityCheck)
  },

  // وظائف العيوب والإصلاحات
  defectsRepairs: {
    getByOrderId: (orderId) => ipcRenderer.invoke('defectsRepairs:getByOrderId', orderId),
    create: (defectRepair) => ipcRenderer.invoke('defectsRepairs:create', defectRepair),
    updateRepair: (id, repairData) => ipcRenderer.invoke('defectsRepairs:updateRepair', id, repairData),
    verify: (id, verificationData) => ipcRenderer.invoke('defectsRepairs:verify', id, verificationData)
  },

  // وظائف المواد الخام
  materials: {
    getAll: () => ipcRenderer.invoke('materials:getAll'),
    getAllWithInventory: () => ipcRenderer.invoke('materials:getAllWithInventory'),
    getById: (id) => ipcRenderer.invoke('materials:getById', id),
    create: (material) => ipcRenderer.invoke('materials:create', material),
    update: (id, material) => ipcRenderer.invoke('materials:update', id, material),
    delete: (id) => ipcRenderer.invoke('materials:delete', id),
    search: (query) => ipcRenderer.invoke('materials:search', query)
  },

  // وظائف المخزون
  inventory: {
    getAll: () => ipcRenderer.invoke('inventory:getAll'),
    getByMaterialId: (materialId) => ipcRenderer.invoke('inventory:getByMaterialId', materialId),
    update: (materialId, quantity) => ipcRenderer.invoke('inventory:update', materialId, quantity),
    getLowStock: () => ipcRenderer.invoke('inventory:getLowStock'),
    getLowInventoryItems: () => ipcRenderer.invoke('inventory:getLowInventoryItems')
  },

  // وظائف الطلبات
  orders: {
    getAll: () => ipcRenderer.invoke('orders:getAll'),
    getById: (id) => ipcRenderer.invoke('orders:getById', id),
    create: (order) => ipcRenderer.invoke('orders:create', order),
    createCustom: (order) => ipcRenderer.invoke('orders:createCustom', order),
    update: (id, order) => ipcRenderer.invoke('orders:update', id, order),
    delete: (id) => ipcRenderer.invoke('orders:delete', id),
    search: (query) => ipcRenderer.invoke('orders:search', query),
    getByStatus: (status) => ipcRenderer.invoke('orders:getByStatus', status),
    updateStatus: (id, status) => ipcRenderer.invoke('orders:updateStatus', id, status),
    calculatePrice: (orderData) => ipcRenderer.invoke('orders:calculatePrice', orderData),
    predictCost: (productId, specifications) => ipcRenderer.invoke('orders:predictCost', productId, specifications),
    getLateOrders: () => ipcRenderer.invoke('orders:getLateOrders'),
    getMaterials: (orderId) => ipcRenderer.invoke('orderMaterials:getByOrderId', orderId)
  },

  // وظائف المواد المستخدمة في الطلبات
  orderMaterials: {
    getByOrderId: (orderId) => ipcRenderer.invoke('orderMaterials:getByOrderId', orderId),
    add: (orderMaterial) => ipcRenderer.invoke('orderMaterials:add', orderMaterial),
    update: (id, orderMaterial) => ipcRenderer.invoke('orderMaterials:update', id, orderMaterial),
    delete: (id) => ipcRenderer.invoke('orderMaterials:delete', id)
  },

  // وظائف العاملين
  workers: {
    getAll: () => ipcRenderer.invoke('workers:getAll'),
    getById: (id) => ipcRenderer.invoke('workers:getById', id),
    create: (worker) => ipcRenderer.invoke('workers:create', worker),
    update: (id, worker) => ipcRenderer.invoke('workers:update', id, worker),
    delete: (id) => ipcRenderer.invoke('workers:delete', id),
    search: (query) => ipcRenderer.invoke('workers:search', query),
    getByRole: (role) => ipcRenderer.invoke('workers:getByRole', role)
  },

  // وظائف السلف والدفعات المقدمة
  workerAdvances: {
    getAll: () => ipcRenderer.invoke('workerAdvances:getAll'),
    getByWorkerId: (workerId) => ipcRenderer.invoke('workerAdvances:getByWorkerId', workerId),
    create: (advance) => ipcRenderer.invoke('workerAdvances:create', advance),
    update: (id, advance) => ipcRenderer.invoke('workerAdvances:update', id, advance),
    delete: (id) => ipcRenderer.invoke('workerAdvances:delete', id)
  },

  // وظائف الخصومات
  workerDeductions: {
    getAll: () => ipcRenderer.invoke('workerDeductions:getAll'),
    getByWorkerId: (workerId) => ipcRenderer.invoke('workerDeductions:getByWorkerId', workerId),
    create: (deduction) => ipcRenderer.invoke('workerDeductions:create', deduction)
  },

  // وظائف المكافآت
  workerBonuses: {
    getAll: () => ipcRenderer.invoke('workerBonuses:getAll'),
    getByWorkerId: (workerId) => ipcRenderer.invoke('workerBonuses:getByWorkerId', workerId),
    create: (bonus) => ipcRenderer.invoke('workerBonuses:create', bonus)
  },

  // وظائف كشوف المرتبات
  monthlyPayrolls: {
    getAll: () => ipcRenderer.invoke('monthlyPayrolls:getAll'),
    getByWorkerId: (workerId) => ipcRenderer.invoke('monthlyPayrolls:getByWorkerId', workerId),
    getByPeriod: (month, year) => ipcRenderer.invoke('monthlyPayrolls:getByPeriod', month, year),
    calculateForWorker: (workerId, month, year) => ipcRenderer.invoke('monthlyPayrolls:calculateForWorker', workerId, month, year),
    create: (payroll) => ipcRenderer.invoke('monthlyPayrolls:create', payroll)
  },

  // وظائف تعيين العمال للطلبات
  orderWorkers: {
    getByOrderId: (orderId) => ipcRenderer.invoke('orderWorkers:getByOrderId', orderId),
    getByWorkerId: (workerId) => ipcRenderer.invoke('orderWorkers:getByWorkerId', workerId),
    assign: (orderWorker) => ipcRenderer.invoke('orderWorkers:assign', orderWorker),
    update: (id, orderWorker) => ipcRenderer.invoke('orderWorkers:update', id, orderWorker),
    delete: (id) => ipcRenderer.invoke('orderWorkers:delete', id)
  },

  // وظائف المصنع والتكاليف التشغيلية
  factoryExpenses: {
    getAll: () => ipcRenderer.invoke('factoryExpenses:getAll'),
    getById: (id) => ipcRenderer.invoke('factoryExpenses:getById', id),
    create: (expense) => ipcRenderer.invoke('factoryExpenses:create', expense),
    update: (id, expense) => ipcRenderer.invoke('factoryExpenses:update', id, expense),
    delete: (id) => ipcRenderer.invoke('factoryExpenses:delete', id),
    getByCategory: (category) => ipcRenderer.invoke('factoryExpenses:getByCategory', category),
    getByDateRange: (startDate, endDate) => ipcRenderer.invoke('factoryExpenses:getByDateRange', startDate, endDate)
  },

  // وظائف الفواتير
  invoices: {
    getAll: () => ipcRenderer.invoke('invoices:getAll'),
    getById: (id) => ipcRenderer.invoke('invoices:getById', id),
    getByOrderId: (orderId) => ipcRenderer.invoke('invoices:getByOrderId', orderId),
    create: (invoice) => ipcRenderer.invoke('invoices:create', invoice),
    update: (id, invoice) => ipcRenderer.invoke('invoices:update', id, invoice),
    delete: (id) => ipcRenderer.invoke('invoices:delete', id),
    getByStatus: (status) => ipcRenderer.invoke('invoices:getByStatus', status),
    getByDateRange: (startDate, endDate) => ipcRenderer.invoke('invoices:getByDateRange', startDate, endDate),
    generatePDF: (id) => ipcRenderer.invoke('invoices:generatePDF', id),
    createWithInstallments: (invoice, installmentsData) => ipcRenderer.invoke('invoices:createWithInstallments', invoice, installmentsData)
  },

  // وظائف المدفوعات
  payments: {
    getByInvoiceId: (invoiceId) => ipcRenderer.invoke('payments:getByInvoiceId', invoiceId),
    create: (payment) => ipcRenderer.invoke('payments:create', payment),
    update: (id, payment) => ipcRenderer.invoke('payments:update', id, payment),
    delete: (id) => ipcRenderer.invoke('payments:delete', id),
    getByDateRange: (startDate, endDate) => ipcRenderer.invoke('payments:getByDateRange', startDate, endDate)
  },

  // وظائف الأقساط
  installments: {
    getByInvoiceId: (invoiceId) => ipcRenderer.invoke('installments:getByInvoiceId', invoiceId),
    getById: (id) => ipcRenderer.invoke('installments:getById', id),
    create: (installment) => ipcRenderer.invoke('installments:create', installment),
    update: (id, installment) => ipcRenderer.invoke('installments:update', id, installment),
    delete: (id) => ipcRenderer.invoke('installments:delete', id),
    pay: (id, paymentData) => ipcRenderer.invoke('installments:pay', id, paymentData),
    getDueInstallments: () => ipcRenderer.invoke('installments:getDueInstallments'),
    getLateInstallments: () => ipcRenderer.invoke('installments:getLateInstallments')
  },

  // وظائف العمليات المالية
  financialTransactions: {
    getAll: () => ipcRenderer.invoke('financialTransactions:getAll'),
    getById: (id) => ipcRenderer.invoke('financialTransactions:getById', id),
    create: (transaction) => ipcRenderer.invoke('financialTransactions:create', transaction),
    update: (id, transaction) => ipcRenderer.invoke('financialTransactions:update', id, transaction),
    delete: (id) => ipcRenderer.invoke('financialTransactions:delete', id),
    getByType: (type) => ipcRenderer.invoke('financialTransactions:getByType', type),
    getByCategory: (category) => ipcRenderer.invoke('financialTransactions:getByCategory', category),
    getByDateRange: (startDate, endDate) => ipcRenderer.invoke('financialTransactions:getByDateRange', startDate, endDate),
    getBalance: () => ipcRenderer.invoke('financialTransactions:getBalance')
  },

  // وظائف التقارير
  reports: {
    getSalesReport: (filters) => ipcRenderer.invoke('reports:getSalesReport', filters),
    exportSalesReport: (filters) => ipcRenderer.invoke('reports:exportSalesReport', filters),
    getSalesByPeriod: (period) => ipcRenderer.invoke('reports:getSalesByPeriod', period),
    getExpensesByPeriod: (period) => ipcRenderer.invoke('reports:getExpensesByPeriod', period),
    getProfitByPeriod: (period) => ipcRenderer.invoke('reports:getProfitByPeriod', period),
    getWorkerPerformance: (workerId, period) => ipcRenderer.invoke('reports:getWorkerPerformance', workerId, period),
    getMaterialUsage: (materialId, period) => ipcRenderer.invoke('reports:getMaterialUsage', materialId, period),
    getProductPerformance: (productId, period) => ipcRenderer.invoke('reports:getProductPerformance', productId, period),
    exportToExcel: (reportType, data) => ipcRenderer.invoke('reports:exportToExcel', reportType, data),
    exportToPDF: (reportType, data) => ipcRenderer.invoke('reports:exportToPDF', reportType, data)
  },

  // وظائف المستخدمين
  users: {
    login: (username, password) => ipcRenderer.invoke('users:login', username, password),
    logout: () => ipcRenderer.invoke('users:logout'),
    getCurrentUser: () => ipcRenderer.invoke('users:getCurrentUser'),
    getAll: () => ipcRenderer.invoke('users:getAll'),
    getById: (id) => ipcRenderer.invoke('users:getById', id),
    create: (user) => ipcRenderer.invoke('users:create', user),
    update: (id, user) => ipcRenderer.invoke('users:update', id, user),
    delete: (id) => ipcRenderer.invoke('users:delete', id),
    changePassword: (id, oldPassword, newPassword) => ipcRenderer.invoke('users:changePassword', id, oldPassword, newPassword)
  },

  // وظائف سجل العمليات
  activityLog: {
    getAll: () => ipcRenderer.invoke('activityLog:getAll'),
    getByUserId: (userId) => ipcRenderer.invoke('activityLog:getByUserId', userId),
    getByActionType: (actionType) => ipcRenderer.invoke('activityLog:getByActionType', actionType),
    getByDateRange: (startDate, endDate) => ipcRenderer.invoke('activityLog:getByDateRange', startDate, endDate)
  },

  // وظائف الإعدادات
  settings: {
    get: (key) => ipcRenderer.invoke('settings:get', key),
    set: (key, value) => ipcRenderer.invoke('settings:set', key, value),
    getAll: () => ipcRenderer.invoke('settings:getAll'),
    backup: (path) => ipcRenderer.invoke('settings:backup', path),
    restore: (path) => ipcRenderer.invoke('settings:restore', path)
  },

  // وظائف الإشعارات
  notifications: {
    getAll: () => ipcRenderer.invoke('notifications:getAll'),
    getUnread: () => ipcRenderer.invoke('notifications:getUnread'),
    create: (notification) => ipcRenderer.invoke('notifications:create', notification),
    markAsRead: (id) => ipcRenderer.invoke('notifications:markAsRead', id),
    markAllAsRead: () => ipcRenderer.invoke('notifications:markAllAsRead'),
    delete: (id) => ipcRenderer.invoke('notifications:delete', id),
    deleteAll: () => ipcRenderer.invoke('notifications:deleteAll')
  },

  // وظائف التصدير
  exportToExcel: (data, sheetName) => ipcRenderer.invoke('export:toExcel', data, sheetName)
};

// إضافة مرجع للتوافق مع الكود القديم
window.api = window.electronAPI;
