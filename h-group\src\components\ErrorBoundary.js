import React from 'react';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // تحديث الحالة لإظهار واجهة الخطأ
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // يمكنك تسجيل الخطأ في خدمة تتبع الأخطاء
    console.error('Error Boundary caught an error:', error, errorInfo);
    this.setState({
      error: error,
      errorInfo: errorInfo
    });
  }

  render() {
    if (this.state.hasError) {
      // واجهة مخصصة للخطأ
      return (
        <div style={{
          padding: '2rem',
          background: '#fee2e2',
          color: '#991b1b',
          borderRadius: '8px',
          margin: '2rem',
          fontFamily: 'Cairo, sans-serif',
          direction: 'rtl'
        }}>
          <h2 style={{ marginBottom: '1rem' }}>⚠️ حدث خطأ في التطبيق</h2>
          <p style={{ marginBottom: '1rem' }}>
            عذراً، حدث خطأ غير متوقع. يرجى إعادة تحميل الصفحة أو الاتصال بالدعم الفني.
          </p>
          
          <details style={{ marginTop: '1rem' }}>
            <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
              تفاصيل الخطأ (للمطورين)
            </summary>
            <div style={{
              background: '#fef2f2',
              padding: '1rem',
              borderRadius: '4px',
              marginTop: '1rem',
              overflow: 'auto',
              fontSize: '0.9rem',
              fontFamily: 'monospace',
              direction: 'ltr'
            }}>
              {this.state.error && this.state.error.toString()}
              <br />
              {this.state.errorInfo.componentStack}
            </div>
          </details>

          <div style={{ marginTop: '1.5rem' }}>
            <button
              onClick={() => window.location.reload()}
              style={{
                background: '#dc2626',
                color: 'white',
                border: 'none',
                padding: '0.5rem 1rem',
                borderRadius: '4px',
                cursor: 'pointer',
                marginRight: '0.5rem'
              }}
            >
              إعادة تحميل الصفحة
            </button>
            
            <button
              onClick={() => this.setState({ hasError: false, error: null, errorInfo: null })}
              style={{
                background: '#059669',
                color: 'white',
                border: 'none',
                padding: '0.5rem 1rem',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              المحاولة مرة أخرى
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
