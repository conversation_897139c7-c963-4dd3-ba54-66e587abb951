{"name": "h-group", "version": "1.0.0", "description": "تطبيق إدارة ورشة نجارة/أثاث", "main": "main.js", "scripts": {"start": "npx electron .", "dev": "npx electron . --dev", "build": "npx electron-builder", "pack": "npx electron-builder --dir", "dist": "npx electron-builder", "postinstall": "npx electron-builder install-app-deps"}, "author": "H Group", "license": "MIT", "private": true, "build": {"appId": "com.hgroup.app", "productName": "H Group", "directories": {"output": "dist"}, "files": ["**/*", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!**/node_modules/*.d.ts", "!**/node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "win": {"target": "nsis", "icon": "assets/icons/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "H Group"}}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "better-sqlite3": "^9.6.0", "chart.js": "^4.4.0", "electron-store": "^8.1.0", "exceljs": "^4.3.0", "pdfkit": "^0.13.0", "qrcode": "^1.5.3", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-router-dom": "^6.20.1"}, "devDependencies": {"@babel/core": "^7.22.20", "@babel/preset-env": "^7.22.20", "@babel/preset-react": "^7.22.15", "babel-loader": "^9.1.3", "css-loader": "^6.8.1", "electron": "^28.0.0", "electron-builder": "^24.6.4", "style-loader": "^3.3.3", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}}