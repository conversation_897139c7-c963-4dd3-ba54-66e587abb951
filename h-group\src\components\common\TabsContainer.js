const React = require('react');
const { useNavigate, useLocation } = require('react-router-dom');

/**
 * مكون التبويبات المحسن
 * @param {Object} props - خصائص المكون
 * @param {Array} props.tabs - قائمة التبويبات
 * @param {string} props.activeTab - التبويب النشط
 * @param {Function} props.onTabChange - دالة تغيير التبويب
 * @param {string} props.basePath - المسار الأساسي للتبويبات
 * @param {boolean} props.loading - حالة التحميل
 */
const TabsContainer = ({ tabs, activeTab, onTabChange, basePath, loading = false }) => {
  const navigate = useNavigate();
  const location = useLocation();

  // دالة تغيير التبويب مع تحديث URL
  const handleTabChange = (tabId) => {
    if (tabId === activeTab || loading) return;
    
    // تحديث URL
    const newPath = `${basePath}/${tabId}`;
    navigate(newPath);
    
    // استدعاء دالة التغيير المخصصة
    if (onTabChange) {
      onTabChange(tabId);
    }
  };

  // التحقق من صحة البيانات
  if (!tabs || !Array.isArray(tabs) || tabs.length === 0) {
    return null;
  }

  return React.createElement('div', { className: 'tabs-container' },
    tabs.map(tab => 
      React.createElement('button', {
        key: tab.id,
        className: `tab-btn ${activeTab === tab.id ? 'active' : ''} ${loading ? 'loading' : ''}`,
        onClick: () => handleTabChange(tab.id),
        disabled: loading,
        'aria-selected': activeTab === tab.id,
        'aria-label': tab.label,
        title: tab.label
      },
        // أيقونة التبويب
        tab.icon && React.createElement('i', { 
          className: tab.icon,
          'aria-hidden': 'true'
        }),
        
        // نص التبويب
        React.createElement('span', null, tab.label),
        
        // مؤشر التحميل للتبويب النشط
        loading && activeTab === tab.id && React.createElement('div', { 
          className: 'tab-loading-indicator',
          'aria-hidden': 'true'
        })
      )
    )
  );
};

/**
 * مكون محتوى التبويب
 * @param {Object} props - خصائص المكون
 * @param {string} props.tabId - معرف التبويب
 * @param {string} props.activeTab - التبويب النشط
 * @param {boolean} props.loading - حالة التحميل
 * @param {React.ReactNode} props.children - محتوى التبويب
 */
const TabContent = ({ tabId, activeTab, loading = false, children }) => {
  // عرض المحتوى فقط إذا كان التبويب نشطاً
  if (tabId !== activeTab) {
    return null;
  }

  return React.createElement('div', {
    className: `tab-content ${loading ? 'loading' : ''}`,
    'aria-hidden': tabId !== activeTab,
    role: 'tabpanel',
    'aria-labelledby': `tab-${tabId}`
  }, children);
};

/**
 * مكون التبويبات الكامل مع المحتوى
 * @param {Object} props - خصائص المكون
 * @param {Array} props.tabs - قائمة التبويبات
 * @param {string} props.activeTab - التبويب النشط
 * @param {Function} props.onTabChange - دالة تغيير التبويب
 * @param {string} props.basePath - المسار الأساسي للتبويبات
 * @param {boolean} props.loading - حالة التحميل
 * @param {Object} props.tabContents - محتويات التبويبات
 */
const TabsWithContent = ({ 
  tabs, 
  activeTab, 
  onTabChange, 
  basePath, 
  loading = false, 
  tabContents = {} 
}) => {
  return React.createElement('div', { className: 'tabs-wrapper' },
    // حاوية التبويبات
    React.createElement(TabsContainer, {
      tabs,
      activeTab,
      onTabChange,
      basePath,
      loading
    }),
    
    // محتوى التبويبات
    React.createElement('div', { className: 'tabs-content-wrapper' },
      tabs.map(tab =>
        React.createElement(TabContent, {
          key: tab.id,
          tabId: tab.id,
          activeTab,
          loading
        }, tabContents[tab.id])
      )
    )
  );
};

module.exports = {
  TabsContainer,
  TabContent,
  TabsWithContent
};
