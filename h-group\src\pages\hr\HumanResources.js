const React = require('react');
const { useState, useEffect } = React;
const { Link, useParams, useNavigate } = require('react-router-dom');
const { formatCurrency, formatDate } = require('../../utils/formatters');

const HumanResources = () => {
  const { section } = useParams();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(section || 'workers');
  const [hrData, setHrData] = useState({
    workers: [],
    payrolls: [],
    advances: [],
    totalWorkers: 0,
    activeWorkers: 0,
    totalSalaries: 0,
    pendingAdvances: 0
  });
  const [loading, setLoading] = useState(true);
  const [tabLoading, setTabLoading] = useState(false);

  useEffect(() => {
    loadHRData();
  }, []);

  // تحديث التبويب النشط عند تغيير المسار
  useEffect(() => {
    if (section && ['workers', 'payroll', 'advances'].includes(section)) {
      setActiveTab(section);
    }
  }, [section]);

  const loadHRData = async () => {
    try {
      setLoading(true);

      const [workers, payrolls, advances] = await Promise.all([
        window.electronAPI.workers.getAll(),
        window.electronAPI.monthlyPayrolls.getAll(),
        window.electronAPI.workerAdvances.getAll()
      ]);

      // العمال النشطين
      const activeWorkers = workers.filter(w => w.employment_status === 'active' || !w.employment_status);

      // إجمالي المرتبات للشهر الحالي
      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();
      const currentMonthPayrolls = payrolls.filter(p =>
        p.payroll_month === currentMonth && p.payroll_year === currentYear
      );
      const totalSalaries = currentMonthPayrolls.reduce((sum, p) => sum + (p.net_salary || 0), 0);

      // السلف المعلقة
      const pendingAdvances = advances.filter(a => a.status === 'active' && a.remaining_balance > 0);
      const totalPendingAdvances = pendingAdvances.reduce((sum, a) => sum + (a.remaining_balance || 0), 0);

      setHrData({
        workers,
        payrolls: currentMonthPayrolls,
        advances: pendingAdvances,
        totalWorkers: workers.length,
        activeWorkers: activeWorkers.length,
        totalSalaries,
        pendingAdvances: totalPendingAdvances
      });

    } catch (error) {
      console.error('خطأ في تحميل بيانات الموارد البشرية:', error);
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'workers', label: 'العمال والمصممين', icon: 'fas fa-hard-hat' },
    { id: 'payroll', label: 'المرتبات والأجور', icon: 'fas fa-money-check-alt' },
    { id: 'advances', label: 'السلف والمكافآت', icon: 'fas fa-hand-holding-usd' }
  ];

  // دالة تغيير التبويب مع تحديث المسار
  const handleTabChange = async (tabId) => {
    if (tabId === activeTab) return;

    setTabLoading(true);
    setActiveTab(tabId);
    navigate(`/hr/${tabId}`);

    setTimeout(() => {
      setTabLoading(false);
    }, 300);
  };

  const getWorkerStatusBadge = (status) => {
    const statusMap = {
      'active': { class: 'success', text: 'نشط' },
      'inactive': { class: 'secondary', text: 'غير نشط' },
      'suspended': { class: 'warning', text: 'موقوف' },
      'terminated': { class: 'danger', text: 'منتهي الخدمة' }
    };
    const statusInfo = statusMap[status] || { class: 'success', text: 'نشط' };
    return React.createElement('span', {
      className: `status-badge ${statusInfo.class}`
    }, statusInfo.text);
  };

  if (loading) {
    return React.createElement('div', { className: 'loading-container' },
      React.createElement('div', { className: 'loading-spinner' }),
      React.createElement('p', null, 'جاري تحميل بيانات الموارد البشرية...')
    );
  }

  return React.createElement('div', { className: 'human-resources professional-page' },
    // رأس الصفحة
    React.createElement('div', { className: 'page-header modern-header' },
      React.createElement('div', { className: 'header-content' },
        React.createElement('h1', { className: 'page-title' },
          React.createElement('i', { className: 'fas fa-users-cog' }),
          ' الموارد البشرية'
        ),
        React.createElement('p', { className: 'page-subtitle' },
          'إدارة شاملة للعمال والمرتبات والأجور'
        )
      )
    ),

    // إحصائيات سريعة
    React.createElement('div', { className: 'stats-grid' },
      React.createElement('div', { className: 'stat-card primary' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-users' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'إجمالي العمال'),
          React.createElement('div', { className: 'stat-value' }, hrData.totalWorkers),
          React.createElement('div', { className: 'stat-detail' }, `${hrData.activeWorkers} نشط`)
        )
      ),
      React.createElement('div', { className: 'stat-card success' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-money-bill-wave' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'مرتبات الشهر'),
          React.createElement('div', { className: 'stat-value' }, formatCurrency(hrData.totalSalaries)),
          React.createElement('div', { className: 'stat-detail' }, 'الشهر الحالي')
        )
      ),
      React.createElement('div', { className: 'stat-card warning' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-hand-holding-usd' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'السلف المعلقة'),
          React.createElement('div', { className: 'stat-value' }, formatCurrency(hrData.pendingAdvances)),
          React.createElement('div', { className: 'stat-detail' }, `${hrData.advances.length} سلفة`)
        )
      ),
      React.createElement('div', { className: 'stat-card info' },
        React.createElement('div', { className: 'stat-icon' },
          React.createElement('i', { className: 'fas fa-file-invoice-dollar' })
        ),
        React.createElement('div', { className: 'stat-content' },
          React.createElement('h3', null, 'كشوف المرتبات'),
          React.createElement('div', { className: 'stat-value' }, hrData.payrolls.length),
          React.createElement('div', { className: 'stat-detail' }, 'تم إنشاؤها')
        )
      )
    ),

    // التبويبات
    React.createElement('div', { className: 'modern-card glass-card' },
      React.createElement('div', { className: 'card-header modern-card-header' },
        React.createElement('div', { className: 'tabs-container' },
          tabs.map(tab =>
            React.createElement('button', {
              key: tab.id,
              className: `tab-btn ${activeTab === tab.id ? 'active' : ''}`,
              onClick: () => handleTabChange(tab.id)
            },
              React.createElement('i', { className: tab.icon }),
              React.createElement('span', null, tab.label)
            )
          )
        )
      ),

      React.createElement('div', {
        className: `card-body ${tabLoading ? 'loading' : ''}`
      },
        // تبويب العمال
        activeTab === 'workers' && React.createElement('div', {
          key: 'workers-tab',
          className: 'tab-content'
        },
          React.createElement('div', { className: 'section-header' },
            React.createElement('h3', null, 'العمال والمصممين'),
            React.createElement('div', { className: 'section-actions' },
              React.createElement(Link, {
                to: '/workers/new',
                className: 'btn btn-primary modern-btn'
              },
                React.createElement('i', { className: 'fas fa-plus' }),
                ' إضافة عامل جديد'
              )
            )
          ),
          React.createElement('div', { className: 'table-responsive' },
            React.createElement('table', { className: 'modern-table professional-table' },
              React.createElement('thead', null,
                React.createElement('tr', null,
                  React.createElement('th', null, 'العامل'),
                  React.createElement('th', null, 'الدور'),
                  React.createElement('th', null, 'نوع الأجر'),
                  React.createElement('th', null, 'السعر/المتر'),
                  React.createElement('th', null, 'الهاتف'),
                  React.createElement('th', null, 'الحالة'),
                  React.createElement('th', null, 'الإجراءات')
                )
              ),
              React.createElement('tbody', null,
                hrData.workers.slice(0, 10).map(worker =>
                  React.createElement('tr', { key: worker.id },
                    React.createElement('td', null,
                      React.createElement('div', { className: 'worker-info' },
                        React.createElement('strong', null, worker.name),
                        worker.employee_number && React.createElement('small', null,
                          React.createElement('br'),
                          `رقم: ${worker.employee_number}`
                        )
                      )
                    ),
                    React.createElement('td', null, worker.role || '-'),
                    React.createElement('td', null,
                      worker.payment_type === 'per_meter' ? 'بالمتر' :
                      worker.payment_type === 'fixed_salary' ? 'راتب ثابت' :
                      worker.payment_type === 'hourly' ? 'بالساعة' : 'غير محدد'
                    ),
                    React.createElement('td', null,
                      worker.rate_per_meter ? formatCurrency(worker.rate_per_meter) : '-'
                    ),
                    React.createElement('td', null, worker.phone || '-'),
                    React.createElement('td', null, getWorkerStatusBadge(worker.employment_status)),
                    React.createElement('td', null,
                      React.createElement('div', { className: 'action-buttons' },
                        React.createElement(Link, {
                          to: `/workers/${worker.id}`,
                          className: 'action-btn info-btn',
                          title: 'عرض التفاصيل'
                        },
                          React.createElement('i', { className: 'fas fa-eye' })
                        ),
                        React.createElement(Link, {
                          to: `/workers/edit/${worker.id}`,
                          className: 'action-btn primary-btn',
                          title: 'تعديل'
                        },
                          React.createElement('i', { className: 'fas fa-edit' })
                        )
                      )
                    )
                  )
                )
              )
            )
          ),
          hrData.workers.length > 10 && React.createElement('div', { className: 'text-center mt-3' },
            React.createElement(Link, {
              to: '/workers',
              className: 'btn btn-outline-primary'
            }, 'عرض جميع العمال')
          )
        ),

        // تبويب المرتبات
        activeTab === 'payroll' && React.createElement('div', {
          key: 'payroll-tab',
          className: 'tab-content'
        },
          React.createElement('div', { className: 'section-header' },
            React.createElement('h3', null, 'المرتبات والأجور'),
            React.createElement('div', { className: 'section-actions' },
              React.createElement(Link, {
                to: '/payroll',
                className: 'btn btn-primary modern-btn'
              },
                React.createElement('i', { className: 'fas fa-calculator' }),
                ' إدارة المرتبات'
              )
            )
          ),
          hrData.payrolls.length === 0
            ? React.createElement('div', { className: 'empty-state' },
                React.createElement('i', { className: 'fas fa-file-invoice-dollar' }),
                React.createElement('h3', null, 'لا توجد كشوف مرتبات'),
                React.createElement('p', null, 'لم يتم إنشاء كشوف مرتبات للشهر الحالي'),
                React.createElement(Link, {
                  to: '/payroll',
                  className: 'btn btn-primary'
                }, 'إنشاء كشوف المرتبات')
              )
            : React.createElement('div', { className: 'table-responsive' },
                React.createElement('table', { className: 'modern-table professional-table' },
                  React.createElement('thead', null,
                    React.createElement('tr', null,
                      React.createElement('th', null, 'العامل'),
                      React.createElement('th', null, 'الأمتار المنجزة'),
                      React.createElement('th', null, 'الراتب الإجمالي'),
                      React.createElement('th', null, 'الخصومات'),
                      React.createElement('th', null, 'صافي الراتب'),
                      React.createElement('th', null, 'حالة الدفع'),
                      React.createElement('th', null, 'الإجراءات')
                    )
                  ),
                  React.createElement('tbody', null,
                    hrData.payrolls.map(payroll =>
                      React.createElement('tr', { key: payroll.id },
                        React.createElement('td', null, payroll.worker_name),
                        React.createElement('td', null, `${payroll.total_meters_worked || 0} م²`),
                        React.createElement('td', null, formatCurrency(payroll.gross_salary || 0)),
                        React.createElement('td', null, formatCurrency(payroll.total_deductions || 0)),
                        React.createElement('td', null,
                          React.createElement('strong', { className: 'text-success' },
                            formatCurrency(payroll.net_salary || 0)
                          )
                        ),
                        React.createElement('td', null,
                          React.createElement('span', {
                            className: `status-badge ${
                              payroll.payment_status === 'paid' ? 'success' :
                              payroll.payment_status === 'partially_paid' ? 'warning' : 'danger'
                            }`
                          },
                            payroll.payment_status === 'paid' ? 'مدفوع' :
                            payroll.payment_status === 'partially_paid' ? 'مدفوع جزئياً' : 'معلق'
                          )
                        ),
                        React.createElement('td', null,
                          React.createElement('div', { className: 'action-buttons' },
                            React.createElement('button', {
                              className: 'action-btn info-btn',
                              title: 'عرض كشف المرتب'
                            },
                              React.createElement('i', { className: 'fas fa-eye' })
                            ),
                            React.createElement('button', {
                              className: 'action-btn success-btn',
                              title: 'طباعة'
                            },
                              React.createElement('i', { className: 'fas fa-print' })
                            )
                          )
                        )
                      )
                    )
                  )
                )
              )
        ),

        // تبويب السلف
        activeTab === 'advances' && React.createElement('div', {
          key: 'advances-tab',
          className: 'tab-content'
        },
          React.createElement('div', { className: 'section-header' },
            React.createElement('h3', null, 'السلف والمكافآت'),
            React.createElement('div', { className: 'section-actions' },
              React.createElement(Link, {
                to: '/payroll/advances',
                className: 'btn btn-primary modern-btn'
              },
                React.createElement('i', { className: 'fas fa-plus' }),
                ' إدارة السلف'
              )
            )
          ),
          hrData.advances.length === 0
            ? React.createElement('div', { className: 'empty-state' },
                React.createElement('i', { className: 'fas fa-hand-holding-usd' }),
                React.createElement('h3', null, 'لا توجد سلف معلقة'),
                React.createElement('p', null, 'جميع السلف مسددة أو لا توجد سلف نشطة')
              )
            : React.createElement('div', { className: 'table-responsive' },
                React.createElement('table', { className: 'modern-table professional-table' },
                  React.createElement('thead', null,
                    React.createElement('tr', null,
                      React.createElement('th', null, 'العامل'),
                      React.createElement('th', null, 'مبلغ السلفة'),
                      React.createElement('th', null, 'الرصيد المتبقي'),
                      React.createElement('th', null, 'الخصم الشهري'),
                      React.createElement('th', null, 'تاريخ السلفة'),
                      React.createElement('th', null, 'السبب')
                    )
                  ),
                  React.createElement('tbody', null,
                    hrData.advances.slice(0, 10).map(advance =>
                      React.createElement('tr', { key: advance.id },
                        React.createElement('td', null, advance.worker_name),
                        React.createElement('td', null, formatCurrency(advance.amount)),
                        React.createElement('td', null,
                          React.createElement('span', { className: 'text-warning' },
                            formatCurrency(advance.remaining_balance || 0)
                          )
                        ),
                        React.createElement('td', null, formatCurrency(advance.monthly_deduction_amount || 0)),
                        React.createElement('td', null, formatDate(advance.advance_date)),
                        React.createElement('td', null, advance.reason || '-')
                      )
                    )
                  )
                )
              )
        )
      )
    )
  );
};

module.exports = HumanResources;
