/* أنماط عامة للتطبيق */
:root {
  /* الألوان الرئيسية */
  --primary-color: #1e3a8a; /* أزرق غامق محسن */
  --primary-light-color: #3b82f6; /* أزرق فاتح محسن */
  --primary-dark-color: #1e40af; /* أزرق داكن محسن */
  --secondary-color: #f97316; /* برتقالي محسن */
  --secondary-light-color: #fb923c; /* برتقالي فاتح محسن */
  --secondary-dark-color: #ea580c; /* برتقالي داكن محسن */
  --background-color: #f8fafc;
  --surface-color: #ffffff;
  --text-color: #1f2937;
  --text-light-color: #6b7280;
  --light-color: #fff;
  --border-color: #e5e7eb;
  --border-light-color: #f3f4f6;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #06b6d4;

  /* المسافات */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* الخطوط */
  --font-family: 'Cairo', 'Noto Sans Arabic', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* الظلال */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* الانتقالات */
  --transition-speed: 0.2s;
  --transition-ease: cubic-bezier(0.4, 0, 0.2, 1);

  /* الحدود المدورة */
  --radius-sm: 0.25rem;
  --radius: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

/* إعادة تعيين الأنماط الافتراضية */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-md);
  color: var(--text-color);
  background-color: var(--background-color);
  line-height: 1.6;
  direction: rtl;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-speed);
}

a:hover {
  color: var(--secondary-color);
}

/* الأزرار المحسنة */
button, .btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  font-family: var(--font-family);
  font-size: var(--font-size-sm);
  font-weight: 600;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-lg);
  border: none;
  transition: all var(--transition-speed) var(--transition-ease);
  text-decoration: none;
  text-align: center;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  letter-spacing: 0.025em;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
  color: var(--light-color);
  border: 1px solid var(--primary-dark-color);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--primary-dark-color), var(--primary-color));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark-color));
  color: var(--light-color);
  border: 1px solid var(--secondary-dark-color);
}

.btn-secondary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--secondary-dark-color), var(--secondary-color));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color), #059669);
  color: var(--light-color);
  border: 1px solid #059669;
}

.btn-success:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669, var(--success-color));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning-color), #d97706);
  color: var(--light-color);
  border: 1px solid #d97706;
}

.btn-warning:hover:not(:disabled) {
  background: linear-gradient(135deg, #d97706, var(--warning-color));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color), #dc2626);
  color: var(--light-color);
  border: 1px solid #dc2626;
}

.btn-danger:hover:not(:disabled) {
  background: linear-gradient(135deg, #dc2626, var(--danger-color));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-info {
  background: linear-gradient(135deg, var(--info-color), #0891b2);
  color: var(--light-color);
  border: 1px solid #0891b2;
}

.btn-info:hover:not(:disabled) {
  background: linear-gradient(135deg, #0891b2, var(--info-color));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* أزرار الحدود */
.btn-outline-primary {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-outline-primary:hover:not(:disabled) {
  background: var(--primary-color);
  color: var(--light-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-outline-secondary {
  background: transparent;
  color: var(--secondary-color);
  border: 2px solid var(--secondary-color);
}

.btn-outline-secondary:hover:not(:disabled) {
  background: var(--secondary-color);
  color: var(--light-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* أحجام الأزرار */
.btn-sm {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-xs);
  border-radius: var(--radius);
}

.btn-lg {
  padding: var(--spacing-lg) var(--spacing-xl);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-xl);
}

.btn-xl {
  padding: var(--spacing-xl) var(--spacing-2xl);
  font-size: var(--font-size-xl);
  border-radius: var(--radius-xl);
}

/* أزرار دائرية */
.btn-circle {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-circle.btn-sm {
  width: 36px;
  height: 36px;
}

.btn-circle.btn-lg {
  width: 60px;
  height: 60px;
}

.btn i {
  margin-left: var(--spacing-xs);
}

/* تخطيط التطبيق */
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.header {
  background-color: var(--light-color);
  padding: var(--spacing-md) var(--spacing-lg);
  box-shadow: var(--box-shadow);
  margin-right: 250px;
  z-index: 90;
  color: var(--primary-color);
  font-size: var(--font-size-lg);
  font-weight: bold;
  text-align: center;
}

.content-container {
  display: flex;
  flex: 1;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* الشريط العلوي */
.navbar {
  background-color: var(--primary-color);
  color: var(--light-color);
  padding: var(--spacing-md) 0;
  box-shadow: var(--box-shadow);
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.navbar-brand {
  font-size: var(--font-size-xl);
  font-weight: bold;
  color: var(--light-color);
}

.navbar-menu {
  display: flex;
  gap: var(--spacing-md);
}

.navbar-item {
  color: var(--light-color);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: 4px;
  transition: background-color var(--transition-speed);
}

.navbar-item:hover, .navbar-item.active {
  background-color: rgba(255, 255, 255, 0.1);
}

/* الشريط الجانبي المحسن */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, var(--primary-color), var(--primary-dark-color));
  box-shadow: var(--shadow-xl);
  height: 100vh;
  position: fixed;
  top: 0;
  right: 0;
  overflow-y: auto;
  padding-top: var(--spacing-xl);
  z-index: 100;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: var(--radius);
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.sidebar-menu {
  padding: var(--spacing-lg);
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-lg) var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  border-radius: var(--radius-lg);
  color: rgba(255, 255, 255, 0.9);
  transition: all var(--transition-speed) var(--transition-ease);
  cursor: pointer;
  text-decoration: none;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.sidebar-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(-100%);
  transition: transform var(--transition-speed) var(--transition-ease);
}

.sidebar-item:hover::before {
  transform: translateX(0);
}

.sidebar-item:hover {
  background: rgba(255, 255, 255, 0.15);
  color: var(--light-color);
  transform: translateX(4px);
  box-shadow: var(--shadow);
}

.sidebar-item.active {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark-color));
  color: var(--light-color);
  box-shadow: var(--shadow-md);
  font-weight: 600;
}

.sidebar-item.active::before {
  display: none;
}

.sidebar-item i {
  margin-left: var(--spacing-lg);
  font-size: var(--font-size-xl);
  width: 28px;
  text-align: center;
  transition: transform var(--transition-speed) var(--transition-ease);
}

.sidebar-item:hover i {
  transform: scale(1.1);
}

.sidebar-item span {
  font-size: var(--font-size-md);
  font-weight: inherit;
}

/* المحتوى الرئيسي المحسن */
.main-content {
  margin-right: 280px;
  padding: var(--spacing-2xl);
  flex: 1;
  background: var(--background-color);
  min-height: 100vh;
  position: relative;
  transition: all var(--transition-speed) var(--transition-ease);
}

.main-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.02), rgba(249, 115, 22, 0.02));
  pointer-events: none;
}

/* تحسينات الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
  .main-content {
    margin-right: 0;
    padding: var(--spacing-lg);
  }

  .sidebar {
    transform: translateX(100%);
    transition: transform var(--transition-speed) var(--transition-ease);
  }

  .sidebar.open {
    transform: translateX(0);
  }
}

/* البطاقات المحسنة */
.card {
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
  border: 1px solid var(--border-light-color);
  transition: all var(--transition-speed) var(--transition-ease);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
  color: var(--light-color);
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header h5,
.card-header h4,
.card-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--light-color);
}

.card-title {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
}

.card-body {
  padding: var(--spacing-xl);
  background: var(--surface-color);
}

.card-footer {
  padding: var(--spacing-lg);
  background: var(--border-light-color);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* بطاقات الإحصائيات */
.stat-card {
  background: linear-gradient(135deg, var(--surface-color), #f8fafc);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light-color);
  transition: all var(--transition-speed) var(--transition-ease);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.stat-card .stat-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-xl);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light-color));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow);
}

.stat-card .stat-icon i {
  font-size: 24px;
  color: var(--light-color);
}

.stat-card .stat-content h3 {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-light-color);
  margin-bottom: var(--spacing-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-card .stat-value {
  font-size: var(--font-size-3xl);
  font-weight: 800;
  color: var(--text-color);
  line-height: 1;
}

/* النماذج المحسنة */
.form-group {
  margin-bottom: var(--spacing-lg);
  position: relative;
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
  color: var(--text-color);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.form-control {
  width: 100%;
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  font-family: var(--font-family);
  font-size: var(--font-size-md);
  background: var(--surface-color);
  transition: all var(--transition-speed) var(--transition-ease);
  box-shadow: var(--shadow-sm);
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--shadow);
  transform: translateY(-1px);
}

.form-control:hover {
  border-color: var(--primary-light-color);
}

.form-control::placeholder {
  color: var(--text-light-color);
  opacity: 0.7;
}

/* حقول النماذج مع الأيقونات */
.form-group-icon {
  position: relative;
}

.form-group-icon .form-control {
  padding-right: 3rem;
}

.form-group-icon .form-icon {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light-color);
  font-size: var(--font-size-lg);
  pointer-events: none;
}

/* مجموعات النماذج */
.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.form-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-start;
  align-items: center;
  margin-top: var(--spacing-2xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-light-color);
}

/* حالات النماذج */
.form-control.is-valid {
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.form-control.is-invalid {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-feedback {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.form-feedback.valid-feedback {
  color: var(--success-color);
}

.form-feedback.invalid-feedback {
  color: var(--danger-color);
}

/* الجداول المحسنة */
.table-container {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-sm);
}

.table th {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
  color: var(--light-color);
  font-weight: 600;
  padding: var(--spacing-md) var(--spacing-lg);
  text-align: right;
  font-size: var(--font-size-sm);
  letter-spacing: 0.025em;
  text-transform: uppercase;
  border: none;
  position: sticky;
  top: 0;
  z-index: 10;
}

.table td {
  padding: var(--spacing-md) var(--spacing-lg);
  text-align: right;
  border-bottom: 1px solid var(--border-light-color);
  vertical-align: middle;
  color: var(--text-color);
  font-weight: 500;
}

.table tbody tr {
  transition: all var(--transition-speed) var(--transition-ease);
  border-bottom: 1px solid var(--border-light-color);
}

.table tbody tr:hover {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.05), rgba(249, 115, 22, 0.05));
  transform: scale(1.01);
  box-shadow: var(--shadow-md);
}

.table tbody tr:last-child {
  border-bottom: none;
}

/* جداول مخططة */
.table-striped tbody tr:nth-child(even) {
  background-color: var(--border-light-color);
}

.table-striped tbody tr:nth-child(even):hover {
  background-color: rgba(59, 130, 246, 0.06);
}

/* جداول مدمجة */
.table-compact th,
.table-compact td {
  padding: var(--spacing-sm) var(--spacing-md);
}

/* جداول قابلة للتمرير */
.table-responsive {
  overflow-x: auto;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow);
}

.table-responsive::-webkit-scrollbar {
  height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
  background: var(--border-light-color);
  border-radius: var(--radius);
}

.table-responsive::-webkit-scrollbar-thumb {
  background: var(--primary-light-color);
  border-radius: var(--radius);
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: var(--primary-color);
}

/* الشارات المحسنة */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-speed) var(--transition-ease);
}

.badge:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

.badge-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
  color: var(--light-color);
}

.badge-secondary {
  background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark-color));
  color: var(--light-color);
}

.badge-success {
  background: linear-gradient(135deg, var(--success-color), #059669);
  color: var(--light-color);
}

.badge-warning {
  background: linear-gradient(135deg, var(--warning-color), #d97706);
  color: var(--light-color);
}

.badge-danger {
  background: linear-gradient(135deg, var(--danger-color), #dc2626);
  color: var(--light-color);
}

.badge-info {
  background: linear-gradient(135deg, var(--info-color), #0891b2);
  color: var(--light-color);
}

/* شارات الحالة */
.status-badge {
  position: relative;
  padding-right: var(--spacing-lg);
}

.status-badge::before {
  content: '';
  position: absolute;
  right: var(--spacing-xs);
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* التنبيهات المحسنة */
.alert {
  padding: var(--spacing-lg);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-lg);
  border: 1px solid transparent;
  position: relative;
  box-shadow: var(--shadow);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.alert::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 4px;
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
}

.alert-primary {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-dark-color);
  border-color: rgba(59, 130, 246, 0.2);
}

.alert-primary::before {
  background: var(--primary-color);
}

.alert-success {
  background: rgba(16, 185, 129, 0.1);
  color: #065f46;
  border-color: rgba(16, 185, 129, 0.2);
}

.alert-success::before {
  background: var(--success-color);
}

.alert-warning {
  background: rgba(245, 158, 11, 0.1);
  color: #92400e;
  border-color: rgba(245, 158, 11, 0.2);
}

.alert-warning::before {
  background: var(--warning-color);
}

.alert-danger {
  background: rgba(239, 68, 68, 0.1);
  color: #991b1b;
  border-color: rgba(239, 68, 68, 0.2);
}

.alert-danger::before {
  background: var(--danger-color);
}

.alert-info {
  background: rgba(6, 182, 212, 0.1);
  color: #155e75;
  border-color: rgba(6, 182, 212, 0.2);
}

.alert-info::before {
  background: var(--info-color);
}

.alert-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
}

.alert-message {
  margin: 0;
  opacity: 0.9;
}

/* الشاشات الصغيرة */
/* أنماط الأقساط */
.installments-container {
  margin-bottom: var(--spacing-lg);
}

.installment-card {
  background-color: var(--light-color);
  border-radius: 8px;
  box-shadow: var(--box-shadow);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.installment-info {
  flex: 1;
}

.installment-number {
  font-weight: bold;
  color: var(--primary-color);
  font-size: var(--font-size-lg);
}

.installment-amount {
  font-size: var(--font-size-lg);
  font-weight: bold;
  color: var(--secondary-color);
}

.installment-date {
  color: var(--text-color);
  font-size: var(--font-size-sm);
}

.installment-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 4px;
  font-size: var(--font-size-xs);
  font-weight: bold;
}

.installment-status.paid {
  background-color: rgba(40, 167, 69, 0.1);
  color: var(--success-color);
}

.installment-status.unpaid {
  background-color: rgba(220, 53, 69, 0.1);
  color: var(--danger-color);
}

.installment-status.late {
  background-color: rgba(255, 193, 7, 0.1);
  color: var(--warning-color);
}

.installment-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* أنماط التنبؤ بالتكاليف */
.cost-prediction-container {
  background-color: var(--light-color);
  border-radius: 8px;
  box-shadow: var(--box-shadow);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.cost-prediction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.cost-prediction-title {
  font-size: var(--font-size-lg);
  font-weight: bold;
  color: var(--primary-color);
}

.cost-prediction-accuracy {
  font-size: var(--font-size-sm);
  color: var(--text-color);
}

.cost-prediction-details {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.cost-prediction-item {
  padding: var(--spacing-md);
  background-color: rgba(26, 58, 108, 0.05);
  border-radius: 8px;
}

.cost-prediction-item-label {
  font-size: var(--font-size-sm);
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
}

.cost-prediction-item-value {
  font-size: var(--font-size-lg);
  font-weight: bold;
  color: var(--primary-color);
}

.suggested-materials {
  margin-top: var(--spacing-lg);
}

.suggested-materials-title {
  font-size: var(--font-size-md);
  font-weight: bold;
  margin-bottom: var(--spacing-md);
}

.suggested-material-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.suggested-material-name {
  font-weight: bold;
}

.suggested-material-quantity {
  color: var(--text-color);
}

.suggested-material-add {
  background-color: var(--primary-color);
  color: var(--light-color);
  border: none;
  border-radius: 4px;
  padding: var(--spacing-xs) var(--spacing-sm);
  cursor: pointer;
  transition: background-color var(--transition-speed);
}

.suggested-material-add:hover {
  background-color: var(--primary-dark-color);
}

/* أنماط الفواتير */
.invoice-actions {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.invoice-print-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background-color: var(--primary-color);
  color: var(--light-color);
  border: none;
  border-radius: 4px;
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  transition: background-color var(--transition-speed);
}

.invoice-print-btn:hover {
  background-color: var(--primary-dark-color);
}

.invoice-email-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background-color: var(--secondary-color);
  color: var(--light-color);
  border: none;
  border-radius: 4px;
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  transition: background-color var(--transition-speed);
}

.invoice-email-btn:hover {
  background-color: var(--secondary-dark-color);
}

/* أنماط الإشعارات */
.notifications-menu {
  position: relative;
}

.notifications-toggle {
  background: none;
  border: none;
  color: var(--light-color);
  font-size: var(--font-size-lg);
  cursor: pointer;
  position: relative;
  padding: var(--spacing-sm);
}

.notifications-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--danger-color);
  color: var(--light-color);
  font-size: var(--font-size-xs);
  font-weight: bold;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notifications-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 350px;
  max-height: 400px;
  background-color: var(--light-color);
  border-radius: 8px;
  box-shadow: var(--box-shadow-lg);
  z-index: 1000;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.notifications-header h5 {
  margin: 0;
  font-size: var(--font-size-md);
  color: var(--text-color);
}

.mark-all-read {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: var(--font-size-sm);
  cursor: pointer;
}

.notifications-list {
  overflow-y: auto;
  max-height: 300px;
}

.notification-item {
  display: flex;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: background-color var(--transition-speed);
}

.notification-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.notification-item.unread {
  background-color: rgba(26, 58, 108, 0.1);
}

.notification-icon {
  margin-left: var(--spacing-md);
  font-size: var(--font-size-lg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: bold;
  margin-bottom: var(--spacing-xs);
  color: var(--text-color);
}

.notification-message {
  font-size: var(--font-size-sm);
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
}

.notification-time {
  font-size: var(--font-size-xs);
  color: #777;
}

.notification-delete {
  background: none;
  border: none;
  color: var(--danger-color);
  cursor: pointer;
  opacity: 0.5;
  transition: opacity var(--transition-speed);
}

.notification-delete:hover {
  opacity: 1;
}

.notifications-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  text-align: center;
}

.view-all {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: var(--font-size-sm);
  cursor: pointer;
  width: 100%;
  padding: var(--spacing-sm);
}

.no-notifications {
  padding: var(--spacing-lg);
  text-align: center;
  color: #777;
}

/* أنماط صفحة تسجيل الدخول */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--background-color);
  background-image: linear-gradient(135deg, var(--primary-color), var(--primary-dark-color));
}

.login-card {
  width: 100%;
  max-width: 450px;
  background-color: var(--light-color);
  border-radius: 8px;
  box-shadow: var(--box-shadow-lg);
  overflow: hidden;
}

.login-header {
  padding: var(--spacing-lg);
  text-align: center;
  background-color: var(--primary-color);
  color: var(--light-color);
}

.login-header h1 {
  font-size: var(--font-size-xxl);
  margin-bottom: var(--spacing-sm);
}

.login-header h2 {
  font-size: var(--font-size-md);
  font-weight: normal;
  opacity: 0.9;
}

.login-form {
  padding: var(--spacing-lg);
}

.login-button {
  width: 100%;
  padding: var(--spacing-md);
  margin-top: var(--spacing-md);
  background-color: var(--secondary-color);
  color: var(--light-color);
  font-weight: bold;
  transition: background-color var(--transition-speed);
}

.login-button:hover {
  background-color: var(--secondary-dark-color);
}

.login-footer {
  padding: var(--spacing-md);
  text-align: center;
  border-top: 1px solid var(--border-color);
  color: #777;
  font-size: var(--font-size-sm);
}

/* أنماط القائمة المنسدلة في الشريط الجانبي */
.sidebar-dropdown {
  margin-bottom: var(--spacing-sm);
}

.sidebar-dropdown-content {
  display: none;
  padding-right: var(--spacing-lg);
  margin-top: var(--spacing-xs);
}

.sidebar-dropdown:hover .sidebar-dropdown-content,
.sidebar-dropdown .active + .sidebar-dropdown-content {
  display: block;
}

.sidebar-dropdown-item {
  display: block;
  padding: var(--spacing-sm) var(--spacing-md);
  margin-bottom: var(--spacing-xs);
  border-radius: 4px;
  color: var(--text-color);
  font-size: var(--font-size-sm);
  transition: all var(--transition-speed);
}

.sidebar-dropdown-item:hover, .sidebar-dropdown-item.active {
  background-color: rgba(26, 58, 108, 0.1);
  color: var(--primary-color);
}

.sidebar-dropdown-item i {
  margin-left: var(--spacing-xs);
  font-size: var(--font-size-xs);
}

/* أنماط التحميل */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  font-size: var(--font-size-lg);
  color: var(--primary-color);
  background-color: var(--background-color);
}

.loading::after {
  content: '';
  width: 20px;
  height: 20px;
  margin-right: var(--spacing-sm);
  border: 3px solid var(--primary-color);
  border-top-color: transparent;
  border-radius: 50%;
  animation: loading-spinner 1s linear infinite;
}

@keyframes loading-spinner {
  to {
    transform: rotate(360deg);
  }
}

/* أنماط لوحة المعلومات */
.welcome-section {
  padding: var(--spacing-lg);
}

.welcome-section h1 {
  font-size: var(--font-size-xxl);
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
}

.welcome-section p {
  font-size: var(--font-size-lg);
  color: var(--text-color);
  margin-bottom: var(--spacing-xl);
}

.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-xl);
}

.dashboard-card {
  background-color: var(--light-color);
  border-radius: 8px;
  box-shadow: var(--box-shadow);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  transition: transform var(--transition-speed), box-shadow var(--transition-speed);
}

.dashboard-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-lg);
}

.dashboard-card-icon {
  width: 60px;
  height: 60px;
  background-color: rgba(26, 58, 108, 0.1);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: var(--spacing-md);
}

.dashboard-card-icon i {
  font-size: 24px;
  color: var(--primary-color);
}

.dashboard-card-content {
  flex: 1;
}

.dashboard-card-content h3 {
  font-size: var(--font-size-md);
  color: var(--text-color);
  margin-bottom: var(--spacing-xs);
}

.dashboard-card-value {
  font-size: var(--font-size-xl);
  font-weight: bold;
  color: var(--primary-color);
}

/* أنماط محتوى الأقسام */
.section-content {
  padding: var(--spacing-lg);
}

.section-content h1 {
  font-size: var(--font-size-xl);
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--primary-color);
}

/* الاستجابة للشاشات المختلفة */
@media (max-width: 1200px) {
  .main-content {
    margin-right: 280px;
    padding: var(--spacing-xl);
  }

  .form-row {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 992px) {
  .sidebar {
    width: 250px;
  }

  .main-content {
    margin-right: 250px;
    padding: var(--spacing-lg);
  }

  .stat-card .stat-icon {
    width: 50px;
    height: 50px;
  }

  .stat-card .stat-value {
    font-size: var(--font-size-2xl);
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
    top: 0;
    transform: translateX(-100%);
    transition: transform var(--transition-speed) var(--transition-ease);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .main-content {
    margin-right: 0;
    padding: var(--spacing-md);
  }

  .main-content::before {
    display: none;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .form-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .form-actions .btn {
    width: 100%;
    justify-content: center;
  }

  .cost-prediction-details {
    grid-template-columns: 1fr;
  }

  .invoice-actions {
    flex-direction: column;
  }

  .notifications-dropdown {
    width: 300px;
    right: -100px;
    left: auto;
  }

  .login-card {
    max-width: 90%;
    margin: 0 auto;
  }

  .table-responsive {
    font-size: var(--font-size-xs);
  }

  .table th,
  .table td {
    padding: var(--spacing-sm);
  }

  .card-body {
    padding: var(--spacing-lg);
  }

  .stat-card {
    padding: var(--spacing-lg);
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: var(--spacing-sm);
  }

  .card-body {
    padding: var(--spacing-md);
  }

  .form-control {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .btn {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-xs);
  }

  .stat-card .stat-value {
    font-size: var(--font-size-xl);
  }

  .alert {
    padding: var(--spacing-md);
    flex-direction: column;
    text-align: center;
  }

  .table th,
  .table td {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
  }
}

/* أنماط الطباعة */
@media print {
  .sidebar,
  .btn,
  .form-actions,
  .alert {
    display: none !important;
  }

  .main-content {
    margin-right: 0 !important;
    padding: 0 !important;
  }

  .card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
  }

  .table {
    font-size: 12px !important;
  }

  .table th,
  .table td {
    padding: 4px 8px !important;
  }
}

/* ===== تحسينات التبويبات ===== */

.tabs-container {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 0.5rem;
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;
}

.tabs-container::-webkit-scrollbar {
  height: 4px;
}

.tabs-container::-webkit-scrollbar-track {
  background: transparent;
}

.tabs-container::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 2px;
}

.tab-btn {
  background: none;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #6c757d;
  position: relative;
  min-width: 120px;
  justify-content: center;
}

.tab-btn:hover {
  background-color: #f8f9fa;
  color: #495057;
  transform: translateY(-2px);
}

.tab-btn.active {
  background-color: #2c5aa0;
  color: white;
  box-shadow: 0 4px 12px rgba(44, 90, 160, 0.3);
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #ff6b35;
  border-radius: 2px;
}

.tab-btn i {
  font-size: 1rem;
}

.tab-btn span {
  font-size: 0.9rem;
}

/* محتوى التبويبات */
.tab-content {
  animation: fadeInUp 0.4s ease-out;
  opacity: 1;
  transform: translateY(0);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسين الاستجابة للتبويبات */
@media (max-width: 768px) {
  .tabs-container {
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .tab-btn {
    padding: 0.5rem 1rem;
    min-width: auto;
    flex: 1;
    font-size: 0.85rem;
  }

  .tab-btn span {
    display: none;
  }

  .tab-btn i {
    font-size: 1.1rem;
  }
}

/* تحسين التركيز للتبويبات */
.tab-btn:focus {
  outline: 2px solid #ff6b35;
  outline-offset: 2px;
}

/* تحسين التبويبات في الوضع المظلم */
@media (prefers-color-scheme: dark) {
  .tab-btn {
    color: #adb5bd;
  }

  .tab-btn:hover {
    background-color: #343a40;
    color: #f8f9fa;
  }

  .tabs-container {
    border-bottom-color: #495057;
  }
}

/* تحسين الحالة المعطلة للتبويبات */
.tab-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* تحسين التحميل للتبويبات */
.tab-content.loading {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.tab-content.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #2c5aa0;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* تحسين المحتوى الفارغ */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h3 {
  margin-bottom: 0.5rem;
  color: #495057;
}

.empty-state p {
  margin-bottom: 1.5rem;
  opacity: 0.8;
}

/* ===== تحسينات إضافية للتبويبات ===== */

/* حاوية التبويبات الرئيسية */
.tabs-wrapper {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* محتوى التبويبات */
.tabs-content-wrapper {
  padding: 1.5rem;
  min-height: 400px;
}

/* مؤشر التحميل للتبويب */
.tab-loading-indicator {
  width: 12px;
  height: 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

/* تحسين التبويبات للوضع المضغوط */
.compact-tabs .tab-btn {
  padding: 0.5rem 1rem;
  min-width: 100px;
  font-size: 0.85rem;
}

.compact-tabs .tab-btn span {
  font-size: 0.8rem;
}

/* تحسين التبويبات للشاشات الكبيرة */
@media (min-width: 1200px) {
  .tabs-container {
    gap: 0.75rem;
  }

  .tab-btn {
    padding: 1rem 2rem;
    min-width: 140px;
  }
}

/* تحسين إمكانية الوصول */
.tab-btn:focus-visible {
  outline: 3px solid #ff6b35;
  outline-offset: 2px;
  z-index: 1;
}

/* تحسين الحالة المعطلة */
.tab-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

/* تحسين الانتقالات */
.tab-content {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.tab-content.loading {
  opacity: 0.7;
  transform: scale(0.98);
}

/* ===== التنقل الفرعي ===== */

.sub-navigation {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  padding: 0.75rem 0;
  margin-bottom: 1.5rem;
}

.sub-nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.sub-nav-items {
  display: flex;
  gap: 0.5rem;
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;
}

.sub-nav-items::-webkit-scrollbar {
  height: 3px;
}

.sub-nav-items::-webkit-scrollbar-track {
  background: transparent;
}

.sub-nav-items::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 2px;
}

.sub-nav-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  text-decoration: none;
  color: #6c757d;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  white-space: nowrap;
  border: 1px solid transparent;
}

.sub-nav-item:hover {
  background-color: rgba(44, 90, 160, 0.1);
  color: #2c5aa0;
  text-decoration: none;
  transform: translateY(-1px);
}

.sub-nav-item.active {
  background-color: #2c5aa0;
  color: white;
  box-shadow: 0 2px 8px rgba(44, 90, 160, 0.3);
}

.sub-nav-item i {
  font-size: 0.9rem;
}

/* مؤشر المسار (Breadcrumb) */
.breadcrumb-nav {
  margin-bottom: 1rem;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  padding: 0.5rem 1rem;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 8px;
  list-style: none;
  font-size: 0.85rem;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
}

.breadcrumb-item:not(:last-child)::after {
  content: '/';
  margin: 0 0.5rem;
  color: #6c757d;
  font-weight: normal;
}

.breadcrumb-item a {
  color: #2c5aa0;
  text-decoration: none;
  transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
  color: #ff6b35;
  text-decoration: underline;
}

.breadcrumb-item.active span {
  color: #495057;
  font-weight: 600;
}

/* رأس الصفحة مع التنقل الفرعي */
.page-header-with-subnav {
  margin-bottom: 2rem;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .sub-navigation {
    padding: 0.5rem 0;
  }

  .sub-nav-container {
    padding: 0 0.75rem;
  }

  .sub-nav-item {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .breadcrumb {
    padding: 0.4rem 0.75rem;
    font-size: 0.75rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.5rem;
    align-items: stretch;
  }
}
